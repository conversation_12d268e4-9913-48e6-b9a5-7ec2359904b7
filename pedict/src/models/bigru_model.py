"""
双向GRU模型，用于风电功率预测
"""

import torch
import torch.nn as nn
import numpy as np
from typing import List, Optional, Dict, Any
import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.models.base_model import BaseTimeSeriesModel
from src.utils.config import DATA_PATHS

# BIGRU模型配置
BIGRU_CONFIG = {
    'bigru_units': [128, 64],      # 双向GRU层的单元数
    'dense_units': [64, 32],       # 全连接层的单元数
    'dropout_rate': 0.2,           # Dropout比例
    'bidirectional': True          # 双向标志
}

class BiGRUModel(BaseTimeSeriesModel):
    """双向GRU模型类，用于风电功率预测"""

    def __init__(self,
                 sequence_length: int,
                 n_features: Optional[int] = None,
                 bigru_units: Optional[List[int]] = None,
                 dense_units: Optional[List[int]] = None,
                 dropout_rate: float = 0.2):
        """
        初始化双向GRU模型

        Args:
            sequence_length: 时间序列长度
            n_features: 特征数量
            bigru_units: 双向GRU层的单元数列表
            dense_units: 全连接层的单元数列表
            dropout_rate: Dropout比例
        """
        super().__init__('BiGRU', sequence_length, n_features)

        # 使用默认配置或自定义配置
        self.bigru_units = bigru_units if bigru_units is not None else BIGRU_CONFIG['bigru_units']
        self.dense_units = dense_units if dense_units is not None else BIGRU_CONFIG['dense_units']
        self.dropout_rate = dropout_rate

        # 构建网络层
        if self.n_features is not None:
            self._build_layers()

    def _build_layers(self):
        """构建网络层"""
        print("构建双向GRU模型...")

        if self.n_features is None:
            raise ValueError("特征数量未设置，请在初始化时提供n_features参数")

        # 双向GRU层
        self.bigru_layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        self.dropouts = nn.ModuleList()

        # 第一个双向GRU层
        self.bigru_layers.append(nn.GRU(
            input_size=self.n_features,
            hidden_size=self.bigru_units[0],
            batch_first=True,
            bidirectional=True,  # 双向
            dropout=self.dropout_rate if len(self.bigru_units) > 1 else 0
        ))

        # 批归一化和Dropout（双向输出的维度是hidden_size * 2）
        self.batch_norms.append(nn.BatchNorm1d(self.bigru_units[0] * 2))
        self.dropouts.append(nn.Dropout(self.dropout_rate))

        # 后续双向GRU层
        for i in range(1, len(self.bigru_units)):
            self.bigru_layers.append(nn.GRU(
                input_size=self.bigru_units[i-1] * 2,  # 前一层双向输出
                hidden_size=self.bigru_units[i],
                batch_first=True,
                bidirectional=True,
                dropout=self.dropout_rate if i < len(self.bigru_units) - 1 else 0
            ))

            self.batch_norms.append(nn.BatchNorm1d(self.bigru_units[i] * 2))
            self.dropouts.append(nn.Dropout(self.dropout_rate))

        # 全连接层
        self.dense_layers = nn.ModuleList()
        self.dense_dropouts = nn.ModuleList()

        # 第一个全连接层的输入维度是最后一个双向GRU层的输出维度
        input_dim = self.bigru_units[-1] * 2

        for units in self.dense_units:
            self.dense_layers.append(nn.Linear(input_dim, units))
            self.dense_dropouts.append(nn.Dropout(self.dropout_rate))
            input_dim = units

        # 输出层
        self.output_layer = nn.Linear(input_dim, 1)

        print(f"双向GRU模型构建完成:")
        print(f"  双向GRU层: {self.bigru_units}")
        print(f"  全连接层: {self.dense_units}")
        print(f"  Dropout率: {self.dropout_rate}")

    def forward(self, x):
        """
        前向传播

        Args:
            x: 输入数据，形状为 (batch_size, sequence_length, n_features)

        Returns:
            输出预测值，形状为 (batch_size, 1)
        """
        # 双向GRU层
        for i, (bigru_layer, batch_norm, dropout) in enumerate(zip(self.bigru_layers, self.batch_norms, self.dropouts)):
            if i == 0:
                # 第一层直接使用输入
                bigru_out, _ = bigru_layer(x)
            else:
                # 后续层使用前一层的输出
                bigru_out, _ = bigru_layer(bigru_out)

            # 如果不是最后一个双向GRU层，应用批归一化和dropout
            if i < len(self.bigru_layers) - 1:
                # 批归一化需要调整维度: (batch, seq, hidden*2) -> (batch, hidden*2, seq) -> (batch, seq, hidden*2)
                bigru_out = bigru_out.transpose(1, 2)
                bigru_out = batch_norm(bigru_out)
                bigru_out = bigru_out.transpose(1, 2)
                bigru_out = dropout(bigru_out)

        # 取最后一个时间步的输出
        last_output = bigru_out[:, -1, :]  # (batch_size, hidden_size * 2)

        # 全连接层
        x = last_output
        for dense_layer, dropout in zip(self.dense_layers, self.dense_dropouts):
            x = torch.relu(dense_layer(x))
            x = dropout(x)

        # 输出层
        output = self.output_layer(x)

        return output

    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息

        Returns:
            包含模型信息的字典
        """
        total_params = sum(p.numel() for p in self.parameters()) if hasattr(self, 'bigru_layers') else None

        return {
            'model_name': self.model_name,
            'sequence_length': self.sequence_length,
            'n_features': self.n_features,
            'bigru_units': self.bigru_units,
            'dense_units': self.dense_units,
            'dropout_rate': self.dropout_rate,
            'bidirectional': True,
            'total_params': total_params
        }

# 测试代码
if __name__ == "__main__":
    # 创建测试数据
    sequence_length = 24
    n_features = 10
    batch_size = 32

    # 创建双向GRU模型
    bigru_model = BiGRUModel(sequence_length=sequence_length, n_features=n_features)

    # 获取模型信息
    model_info = bigru_model.get_model_info()
    print("双向GRU模型信息:", model_info)

    # 测试前向传播
    test_input = torch.randn(batch_size, sequence_length, n_features)
    output = bigru_model(test_input)
    print(f"输入形状: {test_input.shape}")
    print(f"输出形状: {output.shape}")
    print("双向GRU模型测试完成！")
