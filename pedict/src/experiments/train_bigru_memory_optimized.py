"""
内存优化的双向GRU模型训练脚本
解决CUDA内存不足问题
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.models.bigru_model import BiGRUModel
from src.utils.visualization import AcademicVisualizer
from src.utils.metrics import ModelEvaluator
from src.utils.memory_optimized_config import (
    DATA_PATHS, MEMORY_OPTIMIZED_CONFIG, MEMORY_OPTIMIZED_BIGRU_CONFIG, 
    setup_matplotlib, DEVICE
)

class MemoryOptimizedBiGRUTrainer:
    """内存优化的双向GRU模型训练器"""
    
    def __init__(self):
        """初始化训练器"""
        self.data_loader = DataLoader()
        # 使用MinMaxScaler对目标变量进行标准化
        self.preprocessor = DataPreprocessor(use_minmax_for_target=True)
        self.visualizer = AcademicVisualizer()
        self.evaluator = ModelEvaluator()
        
        self.model = None
        self.history = None
        self.results = None
        
        # 设置matplotlib
        setup_matplotlib()
        
        print("=" * 60)
        print("内存优化的双向GRU模型训练器已初始化")
        print(f"设备: {DEVICE}")
        print(f"批次大小: {MEMORY_OPTIMIZED_CONFIG['batch_size']}")
        print(f"序列长度: {MEMORY_OPTIMIZED_CONFIG['sequence_length']}")
        print("目标变量标准化方式: MinMaxScaler (0-1范围)")
        print("=" * 60)
    
    def load_and_prepare_data(self) -> dict:
        """
        加载和准备数据
        
        Returns:
            准备好的训练数据
        """
        print("\n" + "=" * 60)
        print("步骤1: 数据加载和预处理")
        print("=" * 60)
        
        # 加载数据
        df = self.data_loader.load_data()
        
        # 显示原始数据的Power统计信息
        print(f"原始Power数据统计:")
        print(f"  最小值: {df['Power'].min():.4f}")
        print(f"  最大值: {df['Power'].max():.4f}")
        print(f"  平均值: {df['Power'].mean():.4f}")
        print(f"  标准差: {df['Power'].std():.4f}")
        
        # 数据预处理
        self.preprocessor.set_data(df)
        df_processed = self.preprocessor.preprocess()
        feature_columns = self.preprocessor.select_features()
        
        # 保存预处理后的数据
        self.preprocessor.save_processed_data()
        
        # 准备训练数据 - 使用内存优化配置
        data = self.preprocessor.prepare_data_for_training(
            sequence_length=MEMORY_OPTIMIZED_CONFIG['sequence_length'],
            train_ratio=MEMORY_OPTIMIZED_CONFIG['train_ratio'],
            val_ratio=MEMORY_OPTIMIZED_CONFIG['val_ratio']
        )

        # 创建PyTorch数据加载器 - 使用内存优化配置
        train_loader, val_loader, test_loader = self.preprocessor.create_pytorch_dataloaders(
            data,
            batch_size=MEMORY_OPTIMIZED_CONFIG['batch_size']
        )

        # 将数据加载器添加到数据字典中
        data['train_loader'] = train_loader
        data['val_loader'] = val_loader
        data['test_loader'] = test_loader

        print(f"数据准备完成，特征数量: {data['n_features']}")
        print(f"使用的标准化器类型: {type(self.preprocessor.scaler_y).__name__}")
        print(f"内存优化设置:")
        print(f"  序列长度: {data['sequence_length']}")
        print(f"  批次大小: {MEMORY_OPTIMIZED_CONFIG['batch_size']}")
        return data
    
    def train_bigru_model(self, data: dict) -> None:
        """
        训练双向GRU模型
        
        Args:
            data: 训练数据
        """
        print("\n" + "=" * 60)
        print("步骤2: 训练内存优化的双向GRU模型")
        print("=" * 60)
        
        # 创建双向GRU模型 - 使用内存优化配置
        self.model = BiGRUModel(
            sequence_length=data['sequence_length'],
            n_features=data['n_features'],
            bigru_units=MEMORY_OPTIMIZED_BIGRU_CONFIG['bigru_units'],
            dense_units=MEMORY_OPTIMIZED_BIGRU_CONFIG['dense_units'],
            dropout_rate=MEMORY_OPTIMIZED_CONFIG['dropout_rate']
        )

        # 设置标准化器
        self.model.set_scalers(
            self.preprocessor.scaler_X,
            self.preprocessor.scaler_y
        )

        # 显示模型信息
        model_info = self.model.get_model_info()
        print(f"模型配置:")
        print(f"  双向GRU层: {model_info['bigru_units']}")
        print(f"  全连接层: {model_info['dense_units']}")
        print(f"  总参数量: {model_info['total_params']:,}")

        # 训练模型 - 使用内存优化配置
        self.history = self.model.train_model(
            train_loader=data['train_loader'],
            val_loader=data['val_loader'],
            epochs=MEMORY_OPTIMIZED_CONFIG['epochs'],
            patience=MEMORY_OPTIMIZED_CONFIG['patience'],
            learning_rate=MEMORY_OPTIMIZED_CONFIG['learning_rate']
        )
        
        # 保存模型
        self.model.save_model()
        
        # 评估模型（使用标准化后的指标）
        self.results = self.model.evaluate(
            X_train=data['X_train'],
            y_train=data['y_train'],
            X_val=data['X_val'],
            y_val=data['y_val'],
            X_test=data['X_test'],
            y_test=data['y_test'],
            use_normalized_metrics=True
        )
        
        print("内存优化的双向GRU模型训练完成！")
    
    def visualize_results(self) -> None:
        """可视化训练结果"""
        print("\n" + "=" * 60)
        print("步骤3: 结果可视化")
        print("=" * 60)
        
        try:
            # 使用单个模型完整结果可视化
            self.visualizer.plot_single_model_results(
                model_name='BiGRU',
                results=self.results,
                history=self.history,
                dataset='test',
                sample_size=200,  # 减少样本数量以节省内存
                save_path=DATA_PATHS['figures']
            )
            
            print("可视化完成！")
        except Exception as e:
            print(f"可视化过程中出现错误: {str(e)}")
            print("跳过可视化步骤...")
    
    def print_detailed_results(self):
        """打印详细的结果"""
        print("\n" + "=" * 80)
        print("内存优化双向GRU模型详细结果 (使用MinMaxScaler标准化目标变量)")
        print("=" * 80)
        
        print(f"\n双向GRU模型结果:")
        print("-" * 40)
        
        for dataset in ['train', 'val', 'test']:
            metrics = self.results[dataset]
            print(f"\n{dataset.capitalize()}集:")
            for metric, value in metrics.items():
                if metric in ['MAPE', 'SMAPE', 'WAPE']:
                    print(f"  {metric}: {value:.4f}%")
                else:
                    print(f"  {metric}: {value:.6f}")
        
        print("\n" + "=" * 80)
        print("内存优化说明:")
        print("- 序列长度减少到12，减少内存使用")
        print("- 批次大小减少到8，避免内存溢出")
        print("- 隐藏单元数减少，降低模型复杂度")
        print("- 使用MinMaxScaler标准化，指标显示为0-1范围内的数值")
        print("- 如果仍有内存问题，会自动切换到CPU训练")
        print("=" * 80)
    
    def run_experiment(self) -> None:
        """运行完整的实验流程"""
        print("开始内存优化的双向GRU风电功率预测模型训练实验")
        print("专门解决CUDA内存不足问题")
        print("=" * 60)
        
        try:
            # 1. 数据准备
            data = self.load_and_prepare_data()
            
            # 2. 训练双向GRU模型
            self.train_bigru_model(data)
            
            # 3. 可视化结果
            self.visualize_results()
            
            # 4. 打印详细结果
            self.print_detailed_results()
            
            # 找到最佳指标
            test_rmse = self.results['test']['RMSE']
            
            print("\n内存优化双向GRU实验成功完成！")
            print(f"测试集RMSE: {test_rmse:.6f}")
            
        except Exception as e:
            print(f"\n实验过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()

# 主程序
if __name__ == "__main__":
    trainer = MemoryOptimizedBiGRUTrainer()
    trainer.run_experiment()
