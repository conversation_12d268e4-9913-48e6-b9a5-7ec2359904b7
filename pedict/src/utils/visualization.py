"""
学术风格的可视化工具模块
"""

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
import os
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import ACADEMIC_COLORS, MODEL_COLORS, DATA_PATHS, setup_matplotlib

class AcademicVisualizer:
    """学术风格的可视化工具类"""
    
    def __init__(self):
        """初始化可视化工具"""
        setup_matplotlib()
        self.colors = ACADEMIC_COLORS
        self.model_colors = MODEL_COLORS
    
    def plot_training_history(self, 
                             histories: Dict[str, Any], 
                             save_path: Optional[str] = None,
                             figsize: Tuple[int, int] = (14, 6)) -> None:
        """
        绘制训练历史对比图
        
        Args:
            histories: 包含不同模型训练历史的字典
            save_path: 保存路径
            figsize: 图像大小
        """
        fig, axes = plt.subplots(1, 2, figsize=figsize)
        
        # 损失函数对比
        ax1 = axes[0]
        for model_name, history in histories.items():
            color = self.model_colors.get(model_name, self.colors['primary'])

            # 适配PyTorch训练历史格式
            if isinstance(history, dict):
                # PyTorch格式
                epochs = range(1, len(history['train_loss']) + 1)
                train_loss = history['train_loss']
                val_loss = history['val_loss']
            else:
                # TensorFlow格式（向后兼容）
                epochs = range(1, len(history.history['loss']) + 1)
                train_loss = history.history['loss']
                val_loss = history.history['val_loss']

            ax1.plot(epochs, train_loss,
                    label=f'{model_name} 训练损失',
                    color=color, linewidth=2, alpha=0.8)
            ax1.plot(epochs, val_loss,
                    label=f'{model_name} 验证损失',
                    color=color, linewidth=2, linestyle='--', alpha=0.8)
        
        ax1.set_title('模型训练损失对比', fontsize=14, fontweight='bold')
        ax1.set_xlabel('训练轮次 (Epochs)')
        ax1.set_ylabel('损失值 (MSE)')
        ax1.legend(frameon=True, fancybox=True, shadow=True)
        ax1.grid(True, alpha=0.3)
        
        # MAE对比
        ax2 = axes[1]
        for model_name, history in histories.items():
            color = self.model_colors.get(model_name, self.colors['primary'])

            # 适配PyTorch训练历史格式
            if isinstance(history, dict):
                # PyTorch格式
                epochs = range(1, len(history['train_mae']) + 1)
                train_mae = history['train_mae']
                val_mae = history['val_mae']
            else:
                # TensorFlow格式（向后兼容）
                epochs = range(1, len(history.history['mae']) + 1)
                train_mae = history.history['mae']
                val_mae = history.history['val_mae']

            ax2.plot(epochs, train_mae,
                    label=f'{model_name} 训练MAE',
                    color=color, linewidth=2, alpha=0.8)
            ax2.plot(epochs, val_mae,
                    label=f'{model_name} 验证MAE',
                    color=color, linewidth=2, linestyle='--', alpha=0.8)
        
        ax2.set_title('模型训练MAE对比', fontsize=14, fontweight='bold')
        ax2.set_xlabel('训练轮次 (Epochs)')
        ax2.set_ylabel('平均绝对误差 (MAE)')
        ax2.legend(frameon=True, fancybox=True, shadow=True)
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            save_file = os.path.join(save_path, 'training_history_comparison.png')
            plt.savefig(save_file, dpi=300, bbox_inches='tight')
            print(f"训练历史对比图已保存到: {save_file}")
        
        plt.show()
    
    def plot_prediction_comparison(self, 
                                  results: Dict[str, Dict], 
                                  dataset: str = 'test',
                                  sample_size: int = 500,
                                  save_path: Optional[str] = None,
                                  figsize: Tuple[int, int] = (16, 10)) -> None:
        """
        绘制预测结果对比图
        
        Args:
            results: 包含不同模型预测结果的字典
            dataset: 数据集类型 ('train', 'val', 'test')
            sample_size: 显示的样本数量
            save_path: 保存路径
            figsize: 图像大小
        """
        n_models = len(results)
        fig, axes = plt.subplots(2, n_models, figsize=figsize)
        
        if n_models == 1:
            axes = axes.reshape(2, 1)
        
        model_names = list(results.keys())
        
        # 第一行：散点图对比
        for i, model_name in enumerate(model_names):
            ax = axes[0, i]
            y_true, y_pred = results[model_name]['predictions'][dataset]
            
            # 随机采样
            if len(y_true) > sample_size:
                indices = np.random.choice(len(y_true), sample_size, replace=False)
                y_true_sample = y_true[indices]
                y_pred_sample = y_pred[indices]
            else:
                y_true_sample = y_true
                y_pred_sample = y_pred
            
            color = self.model_colors.get(model_name, self.colors['primary'])
            ax.scatter(y_true_sample, y_pred_sample, 
                      alpha=0.6, s=20, color=color, edgecolors='white', linewidth=0.5)
            
            # 添加完美预测线
            min_val = min(y_true_sample.min(), y_pred_sample.min())
            max_val = max(y_true_sample.max(), y_pred_sample.max())
            ax.plot([min_val, max_val], [min_val, max_val], 
                   'r--', linewidth=2, alpha=0.8, label='完美预测')
            
            ax.set_xlabel('真实值 (MW)')
            ax.set_ylabel('预测值 (MW)')
            ax.set_title(f'{model_name} - 预测 vs 真实值')
            ax.legend()
            
            # 添加R²值
            from sklearn.metrics import r2_score
            r2 = r2_score(y_true, y_pred)
            ax.text(0.05, 0.95, f'R² = {r2:.3f}', 
                   transform=ax.transAxes, 
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                   fontsize=10, fontweight='bold')
        
        # 第二行：时间序列对比
        display_points = min(sample_size, min([len(results[name]['predictions'][dataset][0]) 
                                             for name in model_names]))
        
        for i, model_name in enumerate(model_names):
            ax = axes[1, i]
            y_true, y_pred = results[model_name]['predictions'][dataset]
            
            time_steps = range(display_points)
            ax.plot(time_steps, y_true[:display_points],
                   label='真实值', color='#212121',  # 深黑色
                   linewidth=2.5, alpha=1.0)

            color = self.model_colors.get(model_name, self.colors['primary'])
            ax.plot(time_steps, y_pred[:display_points],
                   label=f'{model_name}预测值', color=color,
                   linewidth=2.5, alpha=0.9)
            
            ax.set_title(f'{model_name} - 时间序列预测对比')
            ax.set_xlabel('时间步')
            ax.set_ylabel('功率 (MW)')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            save_file = os.path.join(save_path, f'prediction_comparison_{dataset}.png')
            plt.savefig(save_file, dpi=300, bbox_inches='tight')
            print(f"预测结果对比图已保存到: {save_file}")
        
        plt.show()
    
    def plot_metrics_comparison(self, 
                               results: Dict[str, Dict],
                               save_path: Optional[str] = None,
                               figsize: Tuple[int, int] = (14, 8)) -> None:
        """
        绘制评估指标对比图
        
        Args:
            results: 包含不同模型评估结果的字典
            save_path: 保存路径
            figsize: 图像大小
        """
        metrics = ['MAE', 'MSE', 'RMSE', 'R2', 'MAPE']
        datasets = ['train', 'val', 'test']
        
        fig, axes = plt.subplots(2, 3, figsize=figsize)
        axes = axes.flatten()
        
        model_names = list(results.keys())
        x = np.arange(len(model_names))
        width = 0.25
        
        for i, metric in enumerate(metrics):
            ax = axes[i]
            
            for j, dataset in enumerate(datasets):
                values = [results[model][dataset][metric] for model in model_names]
                
                # 选择颜色
                if dataset == 'train':
                    color = self.colors['primary']
                elif dataset == 'val':
                    color = self.colors['secondary']
                else:
                    color = self.colors['accent']
                
                bars = ax.bar(x + j * width, values, width, 
                             label=f'{dataset.capitalize()}集', 
                             color=color, alpha=0.8, edgecolor='white')
                
                # 添加数值标签
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:.3f}', ha='center', va='bottom', fontsize=8)
            
            ax.set_xlabel('模型')
            ax.set_ylabel(metric)
            ax.set_title(f'{metric} 对比')
            ax.set_xticks(x + width)
            ax.set_xticklabels(model_names)
            ax.legend()
            ax.grid(True, alpha=0.3, axis='y')
        
        # 隐藏最后一个子图
        axes[-1].set_visible(False)
        
        plt.tight_layout()
        
        if save_path:
            save_file = os.path.join(save_path, 'metrics_comparison.png')
            plt.savefig(save_file, dpi=300, bbox_inches='tight')
            print(f"评估指标对比图已保存到: {save_file}")
        
        plt.show()
    
    def plot_error_analysis(self, 
                           results: Dict[str, Dict],
                           dataset: str = 'test',
                           save_path: Optional[str] = None,
                           figsize: Tuple[int, int] = (16, 8)) -> None:
        """
        绘制误差分析图
        
        Args:
            results: 包含不同模型预测结果的字典
            dataset: 数据集类型
            save_path: 保存路径
            figsize: 图像大小
        """
        n_models = len(results)
        fig, axes = plt.subplots(2, n_models, figsize=figsize)
        
        if n_models == 1:
            axes = axes.reshape(2, 1)
        
        model_names = list(results.keys())
        
        for i, model_name in enumerate(model_names):
            y_true, y_pred = results[model_name]['predictions'][dataset]
            errors = y_pred - y_true
            
            color = self.model_colors.get(model_name, self.colors['primary'])
            
            # 误差分布直方图
            ax1 = axes[0, i]
            ax1.hist(errors, bins=50, alpha=0.7, color=color, 
                    edgecolor='white', density=True)
            ax1.axvline(x=0, color='red', linestyle='--', linewidth=2)
            ax1.set_title(f'{model_name} - 误差分布')
            ax1.set_xlabel('预测误差 (MW)')
            ax1.set_ylabel('密度')
            ax1.grid(True, alpha=0.3)
            
            # 添加统计信息
            mean_error = np.mean(errors)
            std_error = np.std(errors)
            ax1.text(0.05, 0.95, f'均值: {mean_error:.3f}\n标准差: {std_error:.3f}', 
                    transform=ax1.transAxes, 
                    bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                    fontsize=10, verticalalignment='top')
            
            # 误差 vs 真实值
            ax2 = axes[1, i]
            ax2.scatter(y_true, errors, alpha=0.6, s=15, color=color, 
                       edgecolors='white', linewidth=0.5)
            ax2.axhline(y=0, color='red', linestyle='--', linewidth=2)
            ax2.set_title(f'{model_name} - 误差 vs 真实值')
            ax2.set_xlabel('真实值 (MW)')
            ax2.set_ylabel('预测误差 (MW)')
            ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            save_file = os.path.join(save_path, f'error_analysis_{dataset}.png')
            plt.savefig(save_file, dpi=300, bbox_inches='tight')
            print(f"误差分析图已保存到: {save_file}")
        
        plt.show()

    def plot_combined_prediction_comparison(self,
                                          results: Dict[str, Dict],
                                          dataset: str = 'test',
                                          sample_size: int = 300,
                                          save_path: Optional[str] = None,
                                          figsize: Tuple[int, int] = (16, 8)) -> None:
        """
        绘制原始功率、LSTM预测值和GRU预测值的综合对比图

        Args:
            results: 包含不同模型预测结果的字典
            dataset: 数据集类型 ('train', 'val', 'test')
            sample_size: 显示的样本数量，默认300以便更好观察
            save_path: 保存路径
            figsize: 图像大小
        """
        # 确保有GRU和LSTM两个模型的结果
        if 'GRU' not in results or 'LSTM' not in results:
            print("警告: 需要同时有GRU和LSTM模型的结果才能进行综合对比")
            return

        # 获取预测数据
        gru_true, gru_pred = results['GRU']['predictions'][dataset]
        lstm_true, lstm_pred = results['LSTM']['predictions'][dataset]

        # 确保使用相同的真实值（应该是一样的）
        y_true = gru_true

        # 限制显示的数据点数量
        display_points = min(sample_size, len(y_true))
        time_steps = range(display_points)

        # 创建图形
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize)

        # 第一个子图：完整对比 - 使用更鲜艳的颜色
        ax1.plot(time_steps, y_true[:display_points],
                label='原始功率', color='#212121',  # 深黑色，突出原始数据
                linewidth=3, alpha=1.0, zorder=3)

        ax1.plot(time_steps, gru_pred[:display_points],
                label='GRU预测值', color='#1E88E5',  # 鲜艳蓝色
                linewidth=2.5, alpha=0.9, linestyle='-', zorder=2)

        ax1.plot(time_steps, lstm_pred[:display_points],
                label='LSTM预测值', color='#E91E63',  # 鲜艳粉红色
                linewidth=2.5, alpha=0.9, linestyle='-', zorder=1)

        ax1.set_title('风电功率预测对比 - GRU vs LSTM vs 原始功率',
                     fontsize=14, fontweight='bold')
        ax1.set_xlabel('时间步')
        ax1.set_ylabel('功率 (标准化值)')
        ax1.legend(frameon=True, fancybox=True, shadow=True, loc='upper right')
        ax1.grid(True, alpha=0.3)

        # 添加性能指标文本
        from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score

        gru_mae = mean_absolute_error(y_true, gru_pred)
        gru_rmse = np.sqrt(mean_squared_error(y_true, gru_pred))
        gru_r2 = r2_score(y_true, gru_pred)

        lstm_mae = mean_absolute_error(y_true, lstm_pred)
        lstm_rmse = np.sqrt(mean_squared_error(y_true, lstm_pred))
        lstm_r2 = r2_score(y_true, lstm_pred)

        metrics_text = f'GRU: MAE={gru_mae:.4f}, RMSE={gru_rmse:.4f}, R²={gru_r2:.3f}\n'
        metrics_text += f'LSTM: MAE={lstm_mae:.4f}, RMSE={lstm_rmse:.4f}, R²={lstm_r2:.3f}'

        ax1.text(0.02, 0.98, metrics_text,
                transform=ax1.transAxes,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.9),
                fontsize=10, verticalalignment='top', fontfamily='monospace')

        # 第二个子图：误差对比 - 使用更鲜艳的颜色
        gru_error = gru_pred[:display_points] - y_true[:display_points]
        lstm_error = lstm_pred[:display_points] - y_true[:display_points]

        ax2.plot(time_steps, gru_error,
                label='GRU预测误差', color='#1E88E5',  # 鲜艳蓝色
                linewidth=2, alpha=0.9)

        ax2.plot(time_steps, lstm_error,
                label='LSTM预测误差', color='#E91E63',  # 鲜艳粉红色
                linewidth=2, alpha=0.9)

        ax2.axhline(y=0, color='#FF5722', linestyle='--', linewidth=2.5, alpha=0.8)  # 鲜艳红色零线
        ax2.set_title('预测误差对比', fontsize=14, fontweight='bold')
        ax2.set_xlabel('时间步')
        ax2.set_ylabel('预测误差 (标准化值)')
        ax2.legend(frameon=True, fancybox=True, shadow=True)
        ax2.grid(True, alpha=0.3)

        # 添加误差统计信息
        gru_error_mean = np.mean(np.abs(gru_error))
        lstm_error_mean = np.mean(np.abs(lstm_error))

        error_text = f'平均绝对误差:\nGRU: {gru_error_mean:.4f}\nLSTM: {lstm_error_mean:.4f}'
        ax2.text(0.98, 0.98, error_text,
                transform=ax2.transAxes,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.9),
                fontsize=10, verticalalignment='top', horizontalalignment='right',
                fontfamily='monospace')

        plt.tight_layout()

        if save_path:
            save_file = os.path.join(save_path, f'combined_prediction_comparison_{dataset}.png')
            plt.savefig(save_file, dpi=300, bbox_inches='tight')
            print(f"综合预测对比图已保存到: {save_file}")

        plt.show()

    def plot_single_model_results(self,
                                 model_name: str,
                                 results: Dict,
                                 history: Dict,
                                 dataset: str = 'test',
                                 sample_size: int = 300,
                                 save_path: Optional[str] = None,
                                 figsize: Tuple[int, int] = (16, 12)) -> None:
        """
        绘制单个模型的完整结果图

        Args:
            model_name: 模型名称
            results: 模型结果
            history: 训练历史
            dataset: 数据集类型
            sample_size: 显示的样本数量
            save_path: 保存路径
            figsize: 图像大小
        """
        fig = plt.figure(figsize=figsize)

        # 创建子图布局
        gs = fig.add_gridspec(3, 2, height_ratios=[1, 1, 1], hspace=0.3, wspace=0.3)

        # 获取模型颜色
        model_color = self.model_colors.get(model_name, self.colors['primary'])

        # 1. 训练历史 - 损失
        ax1 = fig.add_subplot(gs[0, 0])
        epochs = range(1, len(history['train_loss']) + 1)
        ax1.plot(epochs, history['train_loss'], label='训练损失',
                color=model_color, linewidth=2, alpha=0.8)
        ax1.plot(epochs, history['val_loss'], label='验证损失',
                color=model_color, linewidth=2, linestyle='--', alpha=0.8)
        ax1.set_title(f'{model_name} - 训练损失', fontsize=12, fontweight='bold')
        ax1.set_xlabel('训练轮次')
        ax1.set_ylabel('损失值 (MSE)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 训练历史 - MAE
        ax2 = fig.add_subplot(gs[0, 1])
        ax2.plot(epochs, history['train_mae'], label='训练MAE',
                color=model_color, linewidth=2, alpha=0.8)
        ax2.plot(epochs, history['val_mae'], label='验证MAE',
                color=model_color, linewidth=2, linestyle='--', alpha=0.8)
        ax2.set_title(f'{model_name} - 训练MAE', fontsize=12, fontweight='bold')
        ax2.set_xlabel('训练轮次')
        ax2.set_ylabel('平均绝对误差')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 预测 vs 真实值散点图
        ax3 = fig.add_subplot(gs[1, 0])
        y_true, y_pred = results['predictions'][dataset]

        # 随机采样
        if len(y_true) > sample_size:
            indices = np.random.choice(len(y_true), sample_size, replace=False)
            y_true_sample = y_true[indices]
            y_pred_sample = y_pred[indices]
        else:
            y_true_sample = y_true
            y_pred_sample = y_pred

        ax3.scatter(y_true_sample, y_pred_sample, alpha=0.6, s=20,
                   color=model_color, edgecolors='white', linewidth=0.5)

        # 完美预测线
        min_val = min(y_true_sample.min(), y_pred_sample.min())
        max_val = max(y_true_sample.max(), y_pred_sample.max())
        ax3.plot([min_val, max_val], [min_val, max_val],
                'r--', linewidth=2, alpha=0.8, label='完美预测')

        ax3.set_xlabel('真实值')
        ax3.set_ylabel('预测值')
        ax3.set_title(f'{model_name} - 预测 vs 真实值')
        ax3.legend()

        # 添加R²值
        from sklearn.metrics import r2_score
        r2 = r2_score(y_true, y_pred)
        ax3.text(0.05, 0.95, f'R² = {r2:.3f}',
                transform=ax3.transAxes,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                fontsize=10, fontweight='bold')

        # 4. 时间序列预测对比
        ax4 = fig.add_subplot(gs[1, 1])
        display_points = min(sample_size, len(y_true))
        time_steps = range(display_points)

        ax4.plot(time_steps, y_true[:display_points],
                label='真实值', color='#212121', linewidth=2.5, alpha=1.0)
        ax4.plot(time_steps, y_pred[:display_points],
                label=f'{model_name}预测值', color=model_color,
                linewidth=2.5, alpha=0.9)

        ax4.set_title(f'{model_name} - 时间序列预测对比')
        ax4.set_xlabel('时间步')
        ax4.set_ylabel('功率 (标准化值)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # 5. 误差分布
        ax5 = fig.add_subplot(gs[2, 0])
        errors = y_pred - y_true
        ax5.hist(errors, bins=50, alpha=0.7, color=model_color,
                edgecolor='white', density=True)
        ax5.axvline(x=0, color='red', linestyle='--', linewidth=2)
        ax5.set_title(f'{model_name} - 误差分布')
        ax5.set_xlabel('预测误差')
        ax5.set_ylabel('密度')
        ax5.grid(True, alpha=0.3)

        # 添加统计信息
        mean_error = np.mean(errors)
        std_error = np.std(errors)
        ax5.text(0.05, 0.95, f'均值: {mean_error:.4f}\n标准差: {std_error:.4f}',
                transform=ax5.transAxes,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                fontsize=10, verticalalignment='top')

        # 6. 性能指标柱状图
        ax6 = fig.add_subplot(gs[2, 1])
        metrics = results[dataset]
        metric_names = ['MAE', 'MSE', 'RMSE', 'R2']
        metric_values = [metrics[name] for name in metric_names if name in metrics]
        metric_labels = [name for name in metric_names if name in metrics]

        bars = ax6.bar(metric_labels, metric_values, color=model_color, alpha=0.8)
        ax6.set_title(f'{model_name} - {dataset.capitalize()}集性能指标')
        ax6.set_ylabel('指标值')

        # 添加数值标签
        for bar, value in zip(bars, metric_values):
            height = bar.get_height()
            ax6.text(bar.get_x() + bar.get_width()/2., height,
                    f'{value:.4f}', ha='center', va='bottom', fontsize=9)

        plt.suptitle(f'{model_name}模型完整结果分析', fontsize=16, fontweight='bold')

        if save_path:
            save_file = os.path.join(save_path, f'{model_name.lower()}_complete_results.png')
            plt.savefig(save_file, dpi=300, bbox_inches='tight')
            print(f"{model_name}完整结果图已保存到: {save_file}")

        plt.show()

# 测试代码
if __name__ == "__main__":
    # 创建可视化工具实例
    visualizer = AcademicVisualizer()
    
    # 测试颜色配置
    print("学术风格颜色配置:")
    for name, color in ACADEMIC_COLORS.items():
        print(f"{name}: {color}")
    
    print("\n模型颜色映射:")
    for name, color in MODEL_COLORS.items():
        print(f"{name}: {color}")
