"""
内存优化的配置文件，用于解决CUDA内存不足问题
"""

import os
import torch

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设备配置 - 优先使用CPU以避免内存问题
if torch.cuda.is_available():
    # 检查GPU内存
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
    print(f"GPU内存: {gpu_memory:.1f}GB")
    
    if gpu_memory < 4.0:  # 如果GPU内存小于4GB，使用CPU
        DEVICE = torch.device('cpu')
        print("GPU内存不足，使用CPU进行训练")
    else:
        DEVICE = torch.device('cuda')
        print("使用GPU进行训练")
else:
    DEVICE = torch.device('cpu')
    print("CUDA不可用，使用CPU进行训练")

# 数据路径
DATA_PATHS = {
    'raw_data': os.path.join(PROJECT_ROOT, 'data', 'raw'),
    'processed_data': os.path.join(PROJECT_ROOT, 'data', 'processed'),
    'models': os.path.join(PROJECT_ROOT, 'results', 'models'),
    'figures': os.path.join(PROJECT_ROOT, 'results', 'figures'),
    'reports': os.path.join(PROJECT_ROOT, 'results', 'reports')
}

# 内存优化的模型配置
MEMORY_OPTIMIZED_CONFIG = {
    'sequence_length': 12,      # 大幅减少序列长度
    'train_ratio': 0.8,
    'val_ratio': 0.1,
    'test_ratio': 0.1,
    'batch_size': 8,            # 大幅减少批次大小
    'epochs': 30,               # 减少训练轮数
    'patience': 8,              # 减少早停耐心值
    'learning_rate': 0.001,
    'dropout_rate': 0.2,        # 减少dropout
    'device': DEVICE,
    'random_seed': 42,
    'num_workers': 0            # 设置为0避免多进程问题
}

# 内存优化的GRU配置
MEMORY_OPTIMIZED_GRU_CONFIG = {
    'name': 'GRU',
    'gru_units': [32, 16],      # 减少隐藏单元数
    'dense_units': [16, 8]      # 减少全连接层单元数
}

# 内存优化的LSTM配置
MEMORY_OPTIMIZED_LSTM_CONFIG = {
    'name': 'LSTM',
    'lstm_units': [32, 16],     # 减少隐藏单元数
    'dense_units': [16, 8]      # 减少全连接层单元数
}

# 内存优化的BiGRU配置
MEMORY_OPTIMIZED_BIGRU_CONFIG = {
    'bigru_units': [32, 16],    # 减少隐藏单元数（双向会自动翻倍）
    'dense_units': [16, 8],     # 减少全连接层单元数
    'dropout_rate': 0.2,
    'bidirectional': True
}

# 特征列表（保持不变）
FEATURE_COLUMNS = [
    'Wind_speed_10m', 'Wind_direction_10m',
    'Wind_speed_30m', 'Wind_direction_30m',
    'Wind_speed_50m', 'Wind_direction_50m',
    'Wind_speed_hub', 'Wind_direction_hub',
    'Air_temperature', 'Atmosphere', 'Relative_humidity',
    'hour_sin', 'hour_cos', 'day_sin', 'day_cos',
    'month_sin', 'month_cos',
    'wind_speed_diff', 'wind_direction_diff',
    'Power_lag1', 'Power_lag2', 'Power_lag3',
    'Power_ma3', 'Power_ma6', 'Wind_speed_ma3'
]

# 颜色配置（保持不变）
ACADEMIC_COLORS = {
    'primary': '#1E88E5',      # 鲜艳蓝色
    'secondary': '#E91E63',    # 鲜艳粉红色
    'accent': '#FF9800',       # 鲜艳橙色
    'success': '#4CAF50',      # 鲜艳绿色
    'info': '#00BCD4',         # 鲜艳青色
    'warning': '#FFC107',      # 鲜艳黄色
    'light': '#F5F5F5',        # 浅灰色
    'dark': '#212121'          # 深黑色
}

MODEL_COLORS = {
    'GRU': '#1E88E5',          # 鲜艳蓝色
    'LSTM': '#E91E63',         # 鲜艳粉红色
    'BiGRU': '#4CAF50',        # 鲜艳绿色
    'Actual': '#212121'        # 深黑色
}

def setup_matplotlib():
    """设置matplotlib的中文字体和样式"""
    import matplotlib.pyplot as plt
    import matplotlib
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 设置图表样式
    plt.style.use('default')
    matplotlib.rcParams.update({
        'figure.figsize': (10, 6),
        'font.size': 10,
        'axes.titlesize': 12,
        'axes.labelsize': 10,
        'xtick.labelsize': 9,
        'ytick.labelsize': 9,
        'legend.fontsize': 9,
        'figure.titlesize': 14,
        'lines.linewidth': 2,
        'grid.alpha': 0.3,
        'axes.grid': True,
        'axes.spines.top': False,
        'axes.spines.right': False,
        'axes.edgecolor': 'gray',
        'axes.linewidth': 0.8
    })
    
    print("成功设置字体: SimHei")

print(f"内存优化配置加载完成")
print(f"设备: {DEVICE}")
print(f"批次大小: {MEMORY_OPTIMIZED_CONFIG['batch_size']}")
print(f"序列长度: {MEMORY_OPTIMIZED_CONFIG['sequence_length']}")
