"""
配置文件，包含模型参数、路径设置和可视化配置
"""

import os
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform

# 尝试导入PyTorch，如果失败则使用CPU设备
try:
    import torch
    DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
except (ImportError, AttributeError):
    # 如果PyTorch未安装或模拟环境中，使用字符串表示设备
    DEVICE = 'cpu'
    print("警告: PyTorch未安装或在模拟环境中，使用CPU设备")

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 数据路径
DATA_PATHS = {
    'raw_data': os.path.join(PROJECT_ROOT, 'data', 'raw'),
    'processed_data': os.path.join(PROJECT_ROOT, 'data', 'processed'),
    'models': os.path.join(PROJECT_ROOT, 'results', 'models'),
    'figures': os.path.join(PROJECT_ROOT, 'results', 'figures'),
    'reports': os.path.join(PROJECT_ROOT, 'results', 'reports')
}

# ⭐⭐⭐ 数据集配置 - 在此处修改数据集路径 ⭐⭐⭐
DATASET_CONFIG = {
    # 可用的数据集 (包含绝对路径，方便直接替换)
    'available_datasets': {
        'original': {
            'file': 'Site_1_standardized.csv',
            'absolute_path': r'E:\WTF\716BIGRU-CEEMDAN\pedict\data\raw\Site_1_standardized.csv',
            'description': '原始标准化数据集',
            'power_range': '0-1 (标准化)',
            'use_case': '通用风电功率预测'
        },
        'high_frequency': {
            'file': 'Site_1_high_frequency_power.csv',
            'absolute_path': r'E:\WTF\716BIGRU-CEEMDAN\fenjie\results\Site_1_high_frequency_power.csv',
            'description': '高频功率分量数据集',
            'power_range': '约-60到70 (高频波动)',
            'use_case': '短期预测、波动性分析'
        },
        'low_frequency': {
            'file': 'Site_1_low_frequency_power.csv',
            'absolute_path': r'E:\WTF\716BIGRU-CEEMDAN\fenjie\results\Site_1_low_frequency_power.csv',
            'description': '低频功率分量数据集',
            'power_range': '约0-1 (趋势分量)',
            'use_case': '长期趋势预测、容量规划'
        },
        'custom': {
            'file': 'custom_dataset.csv',
            'absolute_path': r'E:\WTF\716BIGRU-CEEMDAN\pedict\data\raw\Site_1_high_frequency_power.csv',  # 👈 在此处修改为您的自定义数据集路径
            'description': '自定义数据集',
            'power_range': '根据数据而定',
            'use_case': '自定义用途'
        }
    },

    # ⭐⭐⭐ 当前使用的数据集 (在此处修改来切换数据集) ⭐⭐⭐
    #
    # 🔄 快速切换方法：
    # 1. 将下面的值改为您想要的数据集名称
    # 2. 保存文件
    # 3. 重新运行程序
    #
    # 📊 可用选项：
    # - 'original': 原始标准化数据集 (0-1范围，通用预测)
    # - 'high_frequency': 高频功率分量 (-60到70范围，短期预测)
    # - 'low_frequency': 低频功率分量 (0-1范围，长期趋势)
    # - 'custom': 自定义数据集 (需要先设置absolute_path)
    #
    'current_dataset': 'high_frequency',  # 👈👈👈 在此处修改数据集名称 👈👈👈

    # 数据列配置
    'target_column': 'Power',
    'date_column': 'date',
    'feature_columns': [
        'Wind_speed_10m', 'Wind_direction_10m', 'Wind_speed_30m', 'Wind_direction_30m',
        'Wind_speed_50m', 'Wind_direction_50m', 'Wind_speed_hub', 'Wind_direction_hub',
        'Air_temperature', 'Atmosphere', 'Relative_humidity'
    ]
}

# 获取当前数据集文件路径的函数
def get_current_dataset_path():
    """
    获取当前配置的数据集文件路径

    Returns:
        str: 数据集文件的完整路径
    """
    current_dataset = DATASET_CONFIG['current_dataset']
    dataset_info = DATASET_CONFIG['available_datasets'][current_dataset]

    # 优先使用绝对路径，如果不存在则使用相对路径
    if 'absolute_path' in dataset_info and os.path.exists(dataset_info['absolute_path']):
        return dataset_info['absolute_path']
    else:
        # 回退到相对路径
        return os.path.join(DATA_PATHS['raw_data'], dataset_info['file'])

def get_current_dataset_info():
    """
    获取当前数据集的详细信息

    Returns:
        dict: 包含数据集信息的字典
    """
    current_dataset = DATASET_CONFIG['current_dataset']
    dataset_info = DATASET_CONFIG['available_datasets'][current_dataset].copy()
    dataset_info['name'] = current_dataset
    dataset_info['path'] = get_current_dataset_path()
    return dataset_info

# 模型配置
MODEL_CONFIG = {
    'sequence_length': 24,
    'train_ratio': 0.8,
    'val_ratio': 0.1,
    'test_ratio': 0.1,
    'batch_size': 16,
    'epochs': 50,
    'patience': 10,
    'learning_rate': 0.001,
    'dropout_rate': 0.3,
    'device': DEVICE,
    'random_seed': 42
}

# GRU模型配置
GRU_CONFIG = {
    'name': 'GRU',
    'gru_units': [64, 32],
    'dense_units': [32, 16]
}

# LSTM模型配置
LSTM_CONFIG = {
    'name': 'LSTM',
    'lstm_units': [64, 32],
    'dense_units': [32, 16]
}

# 可视化配置
def setup_matplotlib():
    """设置matplotlib的中文字体和学术风格"""
    
    # 设置学术风格
    try:
        # 尝试使用新版本的seaborn样式
        plt.style.use('seaborn-v0_8-whitegrid')
    except OSError:
        try:
            # 尝试使用旧版本的seaborn样式
            plt.style.use('seaborn-whitegrid')
        except OSError:
            try:
                # 如果seaborn样式都不可用，使用matplotlib内置样式
                plt.style.use('whitegrid')
            except OSError:
                # 最后使用默认样式
                plt.style.use('default')
                print("警告: 无法加载seaborn样式，使用默认样式")
    
    # 解决中文字体问题
    system = platform.system()
    
    if system == 'Windows':
        # Windows系统字体
        fonts = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong']
    elif system == 'Darwin':  # macOS
        fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti']
    else:  # Linux
        fonts = ['WenQuanYi Micro Hei', 'DejaVu Sans']
    
    # 尝试设置字体
    font_set = False
    for font in fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font]
            font_set = True
            print(f"成功设置字体: {font}")
            break
        except:
            continue
    
    if not font_set:
        print("警告: 无法设置中文字体，将使用默认字体")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    
    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False
    
    # 设置学术风格参数
    plt.rcParams.update({
        'figure.figsize': (10, 6),
        'figure.dpi': 300,
        'savefig.dpi': 300,
        'savefig.bbox': 'tight',
        'savefig.pad_inches': 0.1,
        'font.size': 12,
        'axes.titlesize': 14,
        'axes.labelsize': 12,
        'xtick.labelsize': 10,
        'ytick.labelsize': 10,
        'legend.fontsize': 10,
        'lines.linewidth': 2,
        'lines.markersize': 6,
        'grid.alpha': 0.3,
        'axes.grid': True,
        'axes.spines.top': False,
        'axes.spines.right': False,
        'axes.edgecolor': 'gray',
        'axes.linewidth': 0.8
    })

# 鲜艳直观的颜色配置
ACADEMIC_COLORS = {
    'primary': '#1E88E5',      # 鲜艳蓝色
    'secondary': '#E91E63',    # 鲜艳粉红色
    'accent': '#FF9800',       # 鲜艳橙色
    'success': '#4CAF50',      # 鲜艳绿色
    'info': '#00BCD4',         # 鲜艳青色
    'warning': '#FFC107',      # 鲜艳黄色
    'light': '#F5F5F5',        # 浅灰色
    'dark': '#212121'          # 深黑色
}

# 更直观的模型颜色映射
MODEL_COLORS = {
    'GRU': '#1E88E5',          # 鲜艳蓝色 - 更容易识别
    'LSTM': '#E91E63',         # 鲜艳粉红色 - 与蓝色形成强烈对比
    'BiGRU': '#4CAF50',        # 鲜艳绿色 - 双向GRU用绿色
    'Actual': '#212121'        # 深黑色 - 原始数据用黑色突出
}

# 评估指标
METRICS = ['MAE', 'MSE', 'RMSE', 'R2', 'MAPE']

# 特征列表
FEATURE_COLUMNS = [
    'Wind_speed_10m', 'Wind_direction_10m', 'Wind_speed_30m', 'Wind_direction_30m',
    'Wind_speed_50m', 'Wind_direction_50m', 'Wind_speed_hub', 'Wind_direction_hub',
    'Air_temperature', 'Atmosphere', 'Relative_humidity',
    'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos',
    'wind_speed_diff', 'wind_direction_diff',
    'Power_lag1', 'Power_lag2', 'Power_lag3',
    'Power_ma3', 'Power_ma6', 'Wind_speed_ma3'
]

# 初始化matplotlib设置
setup_matplotlib()

print("配置文件加载完成")
print(f"项目根目录: {PROJECT_ROOT}")
print(f"数据路径: {DATA_PATHS}")
