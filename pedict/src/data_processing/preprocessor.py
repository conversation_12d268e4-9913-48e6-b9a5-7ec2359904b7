"""
数据预处理模块，负责数据清洗、特征工程和数据准备
"""

import os
import pandas as pd
import numpy as np
import torch
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from typing import Tuple, Dict, List, Optional, Union
import joblib
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import DATA_PATHS, FEATURE_COLUMNS

class DataPreprocessor:
    """数据预处理类，负责数据清洗、特征工程和数据准备"""
    
    def __init__(self, df: Optional[pd.DataFrame] = None, use_minmax_for_target: bool = True):
        """
        初始化数据预处理器

        Args:
            df: 输入数据框，如果为None则需要后续设置
            use_minmax_for_target: 是否对目标变量使用MinMaxScaler，默认True
        """
        self.df = df
        self.df_processed = None
        self.scaler_X = StandardScaler()
        # 根据参数选择目标变量的标准化方式
        self.scaler_y = MinMaxScaler() if use_minmax_for_target else StandardScaler()
        self.use_minmax_for_target = use_minmax_for_target
        self.feature_columns = []
        
    def set_data(self, df: pd.DataFrame) -> None:
        """
        设置数据
        
        Args:
            df: 输入数据框
        """
        self.df = df
        
    def preprocess(self) -> pd.DataFrame:
        """
        数据预处理，包括缺失值处理、特征工程等
        
        Returns:
            预处理后的数据框
        """
        if self.df is None:
            raise ValueError("数据未设置，请先调用set_data方法")
        
        print("开始数据预处理...")
        
        # 创建副本
        self.df_processed = self.df.copy()
        
        # 处理缺失值（如果有）
        if self.df_processed.isnull().sum().sum() > 0:
            print("处理缺失值...")
            # 对数值列使用前向填充
            numeric_cols = self.df_processed.select_dtypes(include=[np.number]).columns
            self.df_processed[numeric_cols] = self.df_processed[numeric_cols].fillna(method='ffill')
            self.df_processed[numeric_cols] = self.df_processed[numeric_cols].fillna(method='bfill')
        
        # 特征工程
        print("进行特征工程...")
        
        # 时间特征
        self.df_processed['hour'] = self.df_processed['date'].dt.hour
        self.df_processed['day_of_week'] = self.df_processed['date'].dt.dayofweek
        self.df_processed['month'] = self.df_processed['date'].dt.month
        self.df_processed['day_of_year'] = self.df_processed['date'].dt.dayofyear
        
        # 周期性特征编码
        self.df_processed['hour_sin'] = np.sin(2 * np.pi * self.df_processed['hour'] / 24)
        self.df_processed['hour_cos'] = np.cos(2 * np.pi * self.df_processed['hour'] / 24)
        self.df_processed['day_sin'] = np.sin(2 * np.pi * self.df_processed['day_of_week'] / 7)
        self.df_processed['day_cos'] = np.cos(2 * np.pi * self.df_processed['day_of_week'] / 7)
        self.df_processed['month_sin'] = np.sin(2 * np.pi * self.df_processed['month'] / 12)
        self.df_processed['month_cos'] = np.cos(2 * np.pi * self.df_processed['month'] / 12)
        
        # 风速相关特征
        self.df_processed['wind_speed_diff'] = self.df_processed['Wind_speed_hub'] - self.df_processed['Wind_speed_10m']
        self.df_processed['wind_direction_diff'] = self.df_processed['Wind_direction_hub'] - self.df_processed['Wind_direction_10m']
        
        # 滞后特征（前一个时间点的功率）
        self.df_processed['Power_lag1'] = self.df_processed['Power'].shift(1)
        self.df_processed['Power_lag2'] = self.df_processed['Power'].shift(2)
        self.df_processed['Power_lag3'] = self.df_processed['Power'].shift(3)
        
        # 滑动平均特征
        self.df_processed['Power_ma3'] = self.df_processed['Power'].rolling(window=3).mean()
        self.df_processed['Power_ma6'] = self.df_processed['Power'].rolling(window=6).mean()
        self.df_processed['Wind_speed_ma3'] = self.df_processed['Wind_speed_hub'].rolling(window=3).mean()
        
        # 删除包含NaN的行（由于滞后和滑动平均产生的）
        self.df_processed = self.df_processed.dropna()
        
        print(f"预处理后数据形状: {self.df_processed.shape}")
        
        return self.df_processed
    
    def select_features(self, custom_features: Optional[List[str]] = None) -> List[str]:
        """
        选择用于模型训练的特征
        
        Args:
            custom_features: 自定义特征列表，如果为None则使用默认特征
            
        Returns:
            选择的特征列表
        """
        if self.df_processed is None:
            raise ValueError("数据未预处理，请先调用preprocess方法")
        
        # 使用自定义特征或默认特征
        if custom_features:
            feature_columns = custom_features
        else:
            feature_columns = FEATURE_COLUMNS
        
        # 确保所有特征列都存在
        self.feature_columns = [col for col in feature_columns if col in self.df_processed.columns]
        
        print(f"选择的特征数量: {len(self.feature_columns)}")
        print(f"特征列表: {self.feature_columns}")
        
        return self.feature_columns
    
    def save_processed_data(self, file_name: str = 'processed_data.csv') -> str:
        """
        保存预处理后的数据
        
        Args:
            file_name: 保存的文件名
            
        Returns:
            保存的文件路径
        """
        if self.df_processed is None:
            raise ValueError("数据未预处理，请先调用preprocess方法")
        
        save_path = os.path.join(DATA_PATHS['processed_data'], file_name)
        self.df_processed.to_csv(save_path, index=False)
        print(f"预处理后的数据已保存到: {save_path}")
        
        # 保存特征列表
        feature_path = os.path.join(DATA_PATHS['processed_data'], 'feature_columns.txt')
        with open(feature_path, 'w') as f:
            for feature in self.feature_columns:
                f.write(f"{feature}\n")
        print(f"特征列表已保存到: {feature_path}")
        
        return save_path
    
    def create_sequences(self, X: np.ndarray, y: np.ndarray, sequence_length: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建时间序列数据
        
        Args:
            X: 特征数据
            y: 目标变量
            sequence_length: 序列长度
            
        Returns:
            X_seq: 序列化的特征数据
            y_seq: 对应的目标变量
        """
        X_seq, y_seq = [], []
        
        for i in range(len(X) - sequence_length):
            X_seq.append(X[i:(i + sequence_length)])
            y_seq.append(y[i + sequence_length])
            
        return np.array(X_seq), np.array(y_seq)
    
    def prepare_data_for_training(self, sequence_length: int = 24, 
                                 train_ratio: float = 0.7, 
                                 val_ratio: float = 0.15,
                                 target_column: str = 'Power',
                                 scale_data: bool = True) -> Dict:
        """
        准备训练数据，包括数据划分、标准化和序列生成
        
        Args:
            sequence_length: 时间序列长度
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            target_column: 目标变量列名
            scale_data: 是否标准化数据
            
        Returns:
            包含训练、验证和测试数据的字典
        """
        if self.df_processed is None:
            raise ValueError("数据未预处理，请先调用preprocess方法")
        
        if not self.feature_columns:
            self.select_features()
        
        print("准备训练数据...")
        
        # 提取特征和目标变量
        X = self.df_processed[self.feature_columns].values
        y = self.df_processed[target_column].values.reshape(-1, 1)
        
        # 数据标准化
        if scale_data:
            X_scaled = self.scaler_X.fit_transform(X)
            y_scaled = self.scaler_y.fit_transform(y)
            y_scaled = y_scaled.flatten()
        else:
            X_scaled = X
            y_scaled = y.flatten()
        
        # 创建序列数据
        X_seq, y_seq = self.create_sequences(X_scaled, y_scaled, sequence_length)
        
        # 数据集划分
        n_samples = len(X_seq)
        train_end = int(n_samples * train_ratio)
        val_end = int(n_samples * (train_ratio + val_ratio))
        
        X_train = X_seq[:train_end]
        y_train = y_seq[:train_end]
        X_val = X_seq[train_end:val_end]
        y_val = y_seq[train_end:val_end]
        X_test = X_seq[val_end:]
        y_test = y_seq[val_end:]
        
        print(f"训练集形状: X_train: {X_train.shape}, y_train: {y_train.shape}")
        print(f"验证集形状: X_val: {X_val.shape}, y_val: {y_val.shape}")
        print(f"测试集形状: X_test: {X_test.shape}, y_test: {y_test.shape}")
        
        # 保存标准化器
        if scale_data:
            scaler_path = os.path.join(DATA_PATHS['processed_data'], 'scalers.pkl')
            joblib.dump({
                'scaler_X': self.scaler_X,
                'scaler_y': self.scaler_y
            }, scaler_path)
            print(f"标准化器已保存到: {scaler_path}")
        
        return {
            'X_train': X_train,
            'y_train': y_train,
            'X_val': X_val,
            'y_val': y_val,
            'X_test': X_test,
            'y_test': y_test,
            'n_features': X_seq.shape[2],
            'sequence_length': sequence_length
        }

    def create_pytorch_dataloaders(self,
                                  data: Dict,
                                  batch_size: int = 32,
                                  shuffle_train: bool = True) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """
        创建PyTorch数据加载器

        Args:
            data: 包含训练数据的字典
            batch_size: 批次大小
            shuffle_train: 是否打乱训练数据

        Returns:
            train_loader, val_loader, test_loader
        """
        print("创建PyTorch数据加载器...")

        # 转换为PyTorch张量
        X_train_tensor = torch.FloatTensor(data['X_train'])
        y_train_tensor = torch.FloatTensor(data['y_train'].reshape(-1, 1))
        X_val_tensor = torch.FloatTensor(data['X_val'])
        y_val_tensor = torch.FloatTensor(data['y_val'].reshape(-1, 1))
        X_test_tensor = torch.FloatTensor(data['X_test'])
        y_test_tensor = torch.FloatTensor(data['y_test'].reshape(-1, 1))

        # 创建数据集
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
        test_dataset = TensorDataset(X_test_tensor, y_test_tensor)

        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=shuffle_train,
            num_workers=0,  # Windows兼容性
            pin_memory=torch.cuda.is_available()
        )

        val_loader = DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=0,
            pin_memory=torch.cuda.is_available()
        )

        test_loader = DataLoader(
            test_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=0,
            pin_memory=torch.cuda.is_available()
        )

        print(f"数据加载器创建完成:")
        print(f"  训练集批次数: {len(train_loader)}")
        print(f"  验证集批次数: {len(val_loader)}")
        print(f"  测试集批次数: {len(test_loader)}")

        return train_loader, val_loader, test_loader

# 测试代码
if __name__ == "__main__":
    from data_loader import DataLoader
    
    # 加载数据
    loader = DataLoader()
    df = loader.load_data()
    
    # 预处理数据
    preprocessor = DataPreprocessor(df)
    df_processed = preprocessor.preprocess()
    feature_columns = preprocessor.select_features()
    preprocessor.save_processed_data()
    
    # 准备训练数据
    data = preprocessor.prepare_data_for_training()
