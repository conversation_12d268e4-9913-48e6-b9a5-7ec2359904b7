"""
数据加载模块，负责从文件中读取数据
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Tuple, Dict, List, Optional, Union
import sys

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.utils.config import DATA_PATHS, ACADEMIC_COLORS, setup_matplotlib, get_current_dataset_path, get_current_dataset_info

class DataLoader:
    """数据加载类，负责加载和分析原始数据"""
    
    def __init__(self, data_file: str = 'Site_1_high_frequency_power.csv'):
        """
        初始化数据加载器
        
        Args:
            data_file: 数据文件名
        """
        self.data_file = os.path.join(DATA_PATHS['raw_data'], data_file)
        self.df = None
        
    def load_data(self) -> pd.DataFrame:
        """
        加载数据
        
        Returns:
            加载的数据框
        """
        print(f"正在加载数据: {self.data_file}")
        
        try:
            self.df = pd.read_csv(self.data_file)
            
            # 转换日期列
            if 'date' in self.df.columns:
                self.df['date'] = pd.to_datetime(self.df['date'])
            
            print(f"数据加载成功，形状: {self.df.shape}")
            return self.df
            
        except Exception as e:
            print(f"数据加载失败: {str(e)}")
            raise
    
    def analyze_data(self) -> Dict:
        """
        分析数据，生成基本统计信息
        
        Returns:
            包含数据分析结果的字典
        """
        if self.df is None:
            self.load_data()
        
        print("正在分析数据...")
        
        # 基本信息
        info = {
            'shape': self.df.shape,
            'columns': self.df.columns.tolist(),
            'dtypes': self.df.dtypes,
            'missing_values': self.df.isnull().sum(),
            'stats': self.df.describe()
        }
        
        # 打印基本信息
        print(f"数据形状: {info['shape']}")
        print(f"数据列: {info['columns']}")
        print("\n数据类型:")
        print(info['dtypes'])
        
        # 检查缺失值
        missing_values = info['missing_values']
        if missing_values.sum() > 0:
            print("\n缺失值统计:")
            print(missing_values[missing_values > 0])
        else:
            print("\n数据中没有缺失值")
        
        # 基本统计信息
        print("\n数据统计信息:")
        print(info['stats'])
        
        return info
    
    def visualize_data(self, save_path: Optional[str] = None) -> None:
        """
        数据可视化分析
        
        Args:
            save_path: 图像保存路径，如果为None则不保存
        """
        if self.df is None:
            self.load_data()
        
        print("正在生成数据可视化...")
        
        # 设置学术风格
        setup_matplotlib()
        
        # 创建图形
        fig = plt.figure(figsize=(15, 12))
        
        # 1. 功率时间序列图
        ax1 = fig.add_subplot(3, 2, 1)
        ax1.plot(self.df['date'][:1000], self.df['Power'][:1000], 
                color=ACADEMIC_COLORS['primary'], linewidth=1.5)
        ax1.set_title('风电功率时间序列 (前1000个数据点)')
        ax1.set_xlabel('时间')
        ax1.set_ylabel('功率 (MW)')
        ax1.tick_params(axis='x', rotation=45)
        
        # 2. 功率分布直方图
        ax2 = fig.add_subplot(3, 2, 2)
        sns.histplot(self.df['Power'], bins=50, color=ACADEMIC_COLORS['primary'], 
                    kde=True, ax=ax2, edgecolor='white', alpha=0.7)
        ax2.set_title('风电功率分布')
        ax2.set_xlabel('功率 (MW)')
        ax2.set_ylabel('频次')
        
        # 3. 风速与功率的关系
        ax3 = fig.add_subplot(3, 2, 3)
        ax3.scatter(self.df['Wind_speed_hub'][:5000], self.df['Power'][:5000], 
                   alpha=0.5, color=ACADEMIC_COLORS['secondary'], s=15)
        ax3.set_title('轮毂高度风速 vs 功率')
        ax3.set_xlabel('轮毂高度风速 (m/s)')
        ax3.set_ylabel('功率 (MW)')
        
        # 4. 温度与功率的关系
        ax4 = fig.add_subplot(3, 2, 4)
        ax4.scatter(self.df['Air_temperature'][:5000], self.df['Power'][:5000], 
                   alpha=0.5, color=ACADEMIC_COLORS['accent'], s=15)
        ax4.set_title('气温 vs 功率')
        ax4.set_xlabel('气温 (°C)')
        ax4.set_ylabel('功率 (MW)')
        
        # 5. 相关性热力图
        ax5 = fig.add_subplot(3, 2, 5)
        numeric_cols = self.df.select_dtypes(include=[np.number]).columns
        correlation_matrix = self.df[numeric_cols].corr()
        sns.heatmap(correlation_matrix, annot=False, cmap='coolwarm', center=0, ax=ax5)
        ax5.set_title('特征相关性热力图')
        
        # 6. 功率的月度变化
        ax6 = fig.add_subplot(3, 2, 6)
        self.df['month'] = self.df['date'].dt.month
        monthly_power = self.df.groupby('month')['Power'].mean()
        ax6.plot(monthly_power.index, monthly_power.values, marker='o', 
                color=ACADEMIC_COLORS['info'], linewidth=2)
        ax6.set_title('月度平均功率变化')
        ax6.set_xlabel('月份')
        ax6.set_ylabel('平均功率 (MW)')
        ax6.set_xticks(range(1, 13))
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图像
        if save_path:
            save_file = os.path.join(save_path, 'data_analysis.png')
            plt.savefig(save_file, dpi=300, bbox_inches='tight')
            print(f"数据分析图表已保存到: {save_file}")
        
        plt.show()
        
        # 额外的相关性分析
        plt.figure(figsize=(12, 10))
        
        # 选择与功率相关的主要特征
        power_corr = correlation_matrix['Power'].sort_values(ascending=False)
        top_features = power_corr.index[:10]  # 取相关性最高的10个特征
        
        # 绘制相关性条形图
        plt.barh(top_features, power_corr[top_features], color=ACADEMIC_COLORS['primary'])
        plt.title('与功率相关性最高的特征')
        plt.xlabel('相关系数')
        plt.grid(axis='x', alpha=0.3)
        
        # 保存图像
        if save_path:
            save_file = os.path.join(save_path, 'power_correlation.png')
            plt.savefig(save_file, dpi=300, bbox_inches='tight')
            print(f"功率相关性图表已保存到: {save_file}")
        
        plt.show()

# 测试代码
if __name__ == "__main__":
    loader = DataLoader()
    df = loader.load_data()
    loader.analyze_data()
    loader.visualize_data(save_path=DATA_PATHS['figures'])
