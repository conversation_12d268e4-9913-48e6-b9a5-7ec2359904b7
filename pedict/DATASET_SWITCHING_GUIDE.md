# 🔄 数据集切换指南

## 📋 概述
本指南说明如何在配置文件中快速切换不同的数据集，无需修改代码。

## ⭐ 快速切换方法

### 1. 打开配置文件
编辑文件：`pedict/src/utils/config.py`

### 2. 找到数据集配置部分
在文件中找到以下标记的部分：
```python
# ⭐⭐⭐ 数据集配置 - 在此处修改数据集路径 ⭐⭐⭐
DATASET_CONFIG = {
    # ...
    
    # ⭐ 当前使用的数据集 (在此处修改来切换数据集) ⭐
    'current_dataset': 'high_frequency',  # 👈 修改这里
    
    # ...
}
```

### 3. 修改当前数据集
将 `'current_dataset'` 的值改为以下选项之一：

| 选项 | 描述 | 用途 |
|------|------|------|
| `'original'` | 原始标准化数据集 | 通用风电功率预测 |
| `'high_frequency'` | 高频功率分量数据集 | 短期预测、波动性分析 |
| `'low_frequency'` | 低频功率分量数据集 | 长期趋势预测、容量规划 |
| `'custom'` | 自定义数据集 | 使用您自己的数据 |

### 4. 示例切换

#### 切换到原始数据集：
```python
'current_dataset': 'original',
```

#### 切换到低频数据集：
```python
'current_dataset': 'low_frequency',
```

#### 切换到自定义数据集：
```python
'current_dataset': 'custom',
```

## 🛠️ 添加自定义数据集

### 1. 修改绝对路径
在配置文件中找到 `'custom'` 部分：
```python
'custom': {
    'file': 'custom_dataset.csv',
    'absolute_path': r'E:\WTF\716BIGRU-CEEMDAN\your_custom_dataset.csv',  # 👈 修改这里
    'description': '自定义数据集',
    'power_range': '根据数据而定',
    'use_case': '自定义用途'
}
```

### 2. 替换为您的数据集路径
将 `'absolute_path'` 改为您的数据集的完整路径，例如：
```python
'absolute_path': r'C:\MyData\wind_power_data.csv',
```

### 3. 更新描述信息（可选）
```python
'custom': {
    'file': 'my_wind_data.csv',
    'absolute_path': r'C:\MyData\wind_power_data.csv',
    'description': '我的风电数据集',
    'power_range': '0-100 MW',
    'use_case': '特定场景预测'
}
```

## 📊 数据集要求

### 必需列
您的数据集必须包含以下列：
- `date`: 时间戳
- `Power`: 功率值（目标变量）
- `Wind_speed_10m`, `Wind_direction_10m`: 10米高度风速和风向
- `Wind_speed_30m`, `Wind_direction_30m`: 30米高度风速和风向
- `Wind_speed_50m`, `Wind_direction_50m`: 50米高度风速和风向
- `Wind_speed_hub`, `Wind_direction_hub`: 轮毂高度风速和风向
- `Air_temperature`: 气温
- `Atmosphere`: 大气压
- `Relative_humidity`: 相对湿度

### 数据格式
- CSV格式
- 第一行为列名
- 时间格式：`YYYY-MM-DD HH:MM:SS`

## 🔍 验证切换结果

### 1. 运行测试脚本
```bash
cd pedict
python -c "
from src.data_processing.data_loader import DataLoader
loader = DataLoader()
df = loader.load_data()
print(f'当前数据集形状: {df.shape}')
print(f'Power列范围: {df[\"Power\"].min():.4f} 到 {df[\"Power\"].max():.4f}')
"
```

### 2. 使用数据集管理工具
```bash
cd pedict
python dataset_manager.py
```

## ⚠️ 注意事项

1. **路径格式**：Windows路径使用原始字符串 `r'C:\path\to\file.csv'`
2. **文件存在性**：确保指定的文件路径存在
3. **重启程序**：修改配置后需要重启Python程序
4. **备份配置**：修改前建议备份原始配置文件

## 🚀 快速示例

### 从高频切换到低频数据集：
1. 打开 `pedict/src/utils/config.py`
2. 找到 `'current_dataset': 'high_frequency',`
3. 改为 `'current_dataset': 'low_frequency',`
4. 保存文件
5. 重新运行训练脚本

就这么简单！🎉
