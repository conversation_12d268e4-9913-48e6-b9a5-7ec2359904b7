# 风电功率预测深度学习模型对比研究 (PyTorch版本)

这是一个使用PyTorch深度学习框架实现的风电功率预测学术研究项目。该项目对比了GRU和LSTM两种循环神经网络模型在风电功率预测任务上的性能，具有学术风格的可视化和完整的实验流程。

## 🚀 主要特性

- **PyTorch实现**: 使用PyTorch 2.0+构建的现代深度学习模型
- **模型对比**: GRU vs LSTM性能对比实验
- **学术可视化**: 解决中文字体问题，提供学术风格的图表
- **模块化设计**: 清晰的代码结构，易于扩展和维护
- **完整流程**: 从数据预处理到模型评估的完整实验流程

## 项目结构

```
├── README.md                  # 项目说明文档
├── data/                      # 数据目录
│   ├── raw/                   # 原始数据
│   │   └── Site_1_high_frequency_power.csv
│   └── processed/             # 预处理后的数据
├── src/                       # 源代码目录
│   ├── data_processing/       # 数据处理模块
│   │   ├── __init__.py
│   │   ├── data_loader.py     # 数据加载
│   │   ├── preprocessor.py    # 数据预处理
│   │   └── feature_engineer.py # 特征工程
│   ├── models/                # 模型定义模块
│   │   ├── __init__.py
│   │   ├── base_model.py      # 基础模型类
│   │   ├── gru_model.py       # GRU模型
│   │   └── lstm_model.py      # LSTM模型
│   ├── utils/                 # 工具模块
│   │   ├── __init__.py
│   │   ├── visualization.py   # 可视化工具
│   │   ├── metrics.py         # 评估指标
│   │   └── config.py          # 配置文件
│   └── experiments/           # 实验脚本
│       ├── __init__.py
│       ├── train_models.py    # 模型训练
│       ├── evaluate_models.py # 模型评估
│       └── compare_models.py  # 模型对比
├── results/                   # 结果目录
│   ├── models/                # 保存的模型
│   ├── figures/               # 生成的图表
│   └── reports/               # 实验报告
├── notebooks/                 # Jupyter笔记本
└── requirements.txt           # 依赖包列表
```

## 🔧 环境要求

- Python 3.8+
- PyTorch 2.0+
- CUDA支持（可选，用于GPU加速）
- 其他依赖见 requirements.txt

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd wind-power-prediction

# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 安装PyTorch（CPU版本）
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# 或安装GPU版本（如果有CUDA）
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装其他依赖
pip install -r requirements.txt
```

### 验证安装

```bash
# 运行项目结构测试
python test_pytorch_structure.py
```

## 使用方法

### 1. 快速开始 - 运行完整实验

运行完整的模型训练和对比实验：

```bash
python src/experiments/train_models.py
```

这将执行以下步骤：
1. 加载和分析风电数据集
2. 数据预处理和特征工程
3. 训练GRU和LSTM模型
4. 模型性能对比和评估
5. 生成学术风格的可视化图表
6. 保存模型和实验报告

### 2. 分步骤运行

#### 数据分析
```bash
python src/data_processing/data_loader.py
```

#### 数据预处理
```bash
python src/data_processing/preprocessor.py
```

#### 单独训练模型
```bash
# 训练GRU模型
python src/models/gru_model.py

# 训练LSTM模型
python src/models/lstm_model.py
```

### 3. 使用Jupyter Notebook

项目提供了Jupyter Notebook支持，可以在`notebooks/`目录下创建交互式分析：

```bash
jupyter notebook
```

## 模型架构

项目实现了两种深度学习模型用于对比研究：

### GRU模型
1. 输入层：接收时间序列特征数据 (sequence_length, n_features)
2. GRU层1：64个单元，返回序列
3. 批归一化层 + Dropout层（0.2）
4. GRU层2：32个单元
5. 批归一化层 + Dropout层（0.2）
6. 全连接层1：32个神经元，ReLU激活 + Dropout
7. 全连接层2：16个神经元，ReLU激活 + Dropout
8. 输出层：1个神经元，线性激活（预测功率值）

### LSTM模型
1. 输入层：接收时间序列特征数据 (sequence_length, n_features)
2. LSTM层1：64个单元，返回序列
3. 批归一化层 + Dropout层（0.2）
4. LSTM层2：32个单元
5. 批归一化层 + Dropout层（0.2）
6. 全连接层1：32个神经元，ReLU激活 + Dropout
7. 全连接层2：16个神经元，ReLU激活 + Dropout
8. 输出层：1个神经元，线性激活（预测功率值）

## 特征工程

模型使用以下特征：
- 风速（多个高度）
- 风向（多个高度）
- 气温
- 大气压
- 相对湿度
- 时间特征（小时、星期几、月份等的周期性编码）
- 滞后特征（前几个时间步的功率）
- 滑动平均特征

## 评估指标

模型使用以下指标进行评估：
- MAE（平均绝对误差）
- MSE（均方误差）
- RMSE（均方根误差）
- R²（决定系数）

## 可视化

项目生成多种可视化图表：
- 训练历史（损失和MAE）
- 预测结果对比
- 误差分析
- 残差分析
- 性能总结表

## 注意事项

1. 数据集应包含与原始数据集相同的特征列
2. 时间序列数据应按时间顺序排列
3. 如果新数据缺少某些特征，预测脚本会报错
4. 模型默认使用过去24个时间步预测下一个时间步的功率

## 自定义模型

如需自定义模型参数，可以修改`main.py`中的`config`字典：

```python
config = {
    'sequence_length': 24,  # 使用过去24个时间步预测下一个时间步
    'gru_units': [64, 32],  # GRU层单元数
    'dropout_rate': 0.2,    # Dropout比例
    'learning_rate': 0.001, # 学习率
    'epochs': 100,          # 训练轮数
    'batch_size': 32,       # 批次大小
    'patience': 15          # 早停耐心值
}
```
