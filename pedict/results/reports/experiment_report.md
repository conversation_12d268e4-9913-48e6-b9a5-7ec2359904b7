
# 风电功率预测深度学习模型对比实验报告

## 实验概述
本实验对比了GRU和LSTM两种深度学习模型在风电功率预测任务上的性能。

## 数据集信息
- 数据来源: Site_1_standardized.csv
- 特征数量: 25
- 序列长度: 48
- 训练集比例: 0.8
- 验证集比例: 0.1
- 测试集比例: 0.1

## 模型配置
### GRU模型
- GRU层单元数: [64, 32]
- 全连接层单元数: [32, 16]
- 总参数量: 28,673

### LSTM模型
- LSTM层单元数: [64, 32]
- 全连接层单元数: [32, 16]
- 总参数量: 37,633

## 训练配置
- 批次大小: 32
- 最大训练轮数: 50
- 早停耐心值: 10
- 学习率: 0.001
- Dropout比例: 0.3

## 实验结果

### 测试集性能对比

#### GRU模型
- MAE: 0.0410
- MSE: 0.0024
- RMSE: 0.0492
- R2: 0.9592
- MAPE: 634.3094%

#### LSTM模型
- MAE: 0.0328
- MSE: 0.0019
- RMSE: 0.0431
- R2: 0.9687
- MAPE: 459.3585%

### 最佳模型
基于RMSE指标，最佳模型为: **LSTM** (RMSE: 0.0431)

## 结论
1. 两种模型都能有效预测风电功率
2. LSTM模型在测试集上表现更优
3. 模型具有良好的泛化能力

## 生成的文件
- 训练好的模型: results/models/
- 可视化图表: results/figures/
- 详细数据: results/reports/
