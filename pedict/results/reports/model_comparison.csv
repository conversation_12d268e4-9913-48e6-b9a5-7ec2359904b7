Model,Dataset,Metric,Value
GRU,train,MAE,0.04162565298907628
GRU,train,MSE,0.0027424980684343514
GRU,train,RMSE,0.05236886544918031
GRU,train,R2,0.9531643413503889
GRU,train,MAPE,448.6074069954117
GRU,val,MAE,0.04621468840514065
GRU,val,MSE,0.0038248520043303266
GRU,val,RMSE,0.06184538789861639
GRU,val,R2,0.9468550581854276
GRU,val,MAPE,315.7381915880752
GRU,test,MAE,0.04099423354795691
GRU,test,MSE,0.0024200546159944044
GRU,test,RMSE,0.04919405061584586
GRU,test,R2,0.9591812865270193
GRU,test,MAPE,634.3094451373534
GRU,predictions,train,"(array([0.00448385, 0.01053958, 0.00990938, ..., 0.0638677 , 0.05122744,
       0.00643965]), array([0.05350499, 0.04900035, 0.05279249, ..., 0.14565948, 0.10378353,
       0.09873399], dtype=float32))"
GRU,predictions,val,"(array([0.00370877, 0.00304235, 0.00240491, ..., 0.12689513, 0.08428769,
       0.14559836]), array([0.06432141, 0.06141262, 0.05654193, ..., 0.2679276 , 0.15253596,
       0.11260338], dtype=float32))"
GRU,predictions,test,"(array([0.21154501, 0.14551869, 0.19612316, ..., 0.0443749 , 0.07324829,
       0.12625044]), array([0.30847627, 0.35961658, 0.1540682 , ..., 0.11647592, 0.07189701,
       0.14405206], dtype=float32))"
LSTM,train,MAE,0.0349179057136011
LSTM,train,MSE,0.0023177502920728806
LSTM,train,RMSE,0.04814301914164587
LSTM,train,R2,0.9604180718433346
LSTM,train,MAPE,325.27347360505365
LSTM,val,MAE,0.03952036858754447
LSTM,val,MSE,0.0033134357199979233
LSTM,val,RMSE,0.057562450607995516
LSTM,val,R2,0.9539610033679077
LSTM,val,MAPE,232.8160001279286
LSTM,test,MAE,0.03275967797615562
LSTM,test,MSE,0.0018533284331134603
LSTM,test,RMSE,0.043050301196547514
LSTM,test,R2,0.9687401756214077
LSTM,test,MAPE,459.3584572965543
LSTM,predictions,train,"(array([0.00448385, 0.01053958, 0.00990938, ..., 0.0638677 , 0.05122744,
       0.00643965]), array([0.03847303, 0.0348843 , 0.03656951, ..., 0.12130288, 0.0858678 ,
       0.06371196], dtype=float32))"
LSTM,predictions,val,"(array([0.00370877, 0.00304235, 0.00240491, ..., 0.12689513, 0.08428769,
       0.14559836]), array([0.04508465, 0.03695627, 0.03747055, ..., 0.1877256 , 0.13026503,
       0.09747866], dtype=float32))"
LSTM,predictions,test,"(array([0.21154501, 0.14551869, 0.19612316, ..., 0.0443749 , 0.07324829,
       0.12625044]), array([0.22319585, 0.34299466, 0.1334151 , ..., 0.09513412, 0.0588523 ,
       0.08576009], dtype=float32))"
