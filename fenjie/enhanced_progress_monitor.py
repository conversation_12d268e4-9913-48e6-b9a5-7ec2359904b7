"""
增强的进度监控器
解决IDE中进度条显示问题
"""

import time
import sys
import os

class ProgressMonitor:
    """兼容性进度监控器"""
    
    def __init__(self, total, desc="Progress", unit="it"):
        self.total = total
        self.desc = desc
        self.unit = unit
        self.current = 0
        self.start_time = time.time()
        self.last_print_time = 0
        self.print_interval = 1.0  # 每秒更新一次
        
    def update(self, n=1):
        """更新进度"""
        self.current += n
        current_time = time.time()
        
        # 控制打印频率
        if current_time - self.last_print_time >= self.print_interval or self.current >= self.total:
            self.print_progress()
            self.last_print_time = current_time
    
    def print_progress(self):
        """打印进度信息"""
        elapsed = time.time() - self.start_time
        percentage = (self.current / self.total) * 100
        
        # 计算ETA
        if self.current > 0:
            eta = elapsed * (self.total - self.current) / self.current
        else:
            eta = 0
        
        # 创建进度条
        bar_length = 30
        filled_length = int(bar_length * self.current / self.total)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        
        # 格式化时间
        def format_time(seconds):
            if seconds < 60:
                return f"{seconds:.0f}s"
            elif seconds < 3600:
                return f"{seconds//60:.0f}m{seconds%60:.0f}s"
            else:
                return f"{seconds//3600:.0f}h{(seconds%3600)//60:.0f}m"
        
        # 打印进度
        print(f"\r{self.desc}: {percentage:5.1f}% |{bar}| "
              f"{self.current}/{self.total} [{format_time(elapsed)}<{format_time(eta)}]", 
              end='', flush=True)
        
        if self.current >= self.total:
            print()  # 完成时换行
    
    def close(self):
        """关闭进度条"""
        if self.current < self.total:
            self.current = self.total
            self.print_progress()

class HHOProgressTracker:
    """HHO优化进度跟踪器"""

    def __init__(self, max_iter, n_hawks):
        self.max_iter = max_iter
        self.n_hawks = n_hawks
        self.current_iter = 0
        self.best_fitness = float('inf')
        self.start_time = time.time()
        
        print("🦅 HHO优化进度跟踪器启动")
        print("=" * 60)
        print(f"📊 配置: {max_iter}代 × {n_hawks}鹰")
        print(f"⏰ 开始时间: {time.strftime('%H:%M:%S')}")
        print("=" * 60)
    
    def start_iteration(self, iteration):
        """开始新的迭代"""
        self.current_iter = iteration
        self.iter_start_time = time.time()
        
    def update_fitness(self, fitness, improved=False):
        """更新适应度"""
        if fitness < self.best_fitness:
            self.best_fitness = fitness
            improved = True
        
        # 计算进度
        progress = (self.current_iter + 1) / self.max_iter * 100
        elapsed = time.time() - self.start_time
        iter_time = time.time() - self.iter_start_time
        
        # 估算剩余时间
        if self.current_iter > 0:
            avg_iter_time = elapsed / (self.current_iter + 1)
            eta = avg_iter_time * (self.max_iter - self.current_iter - 1)
        else:
            eta = 0
        
        # 创建状态指示器
        status = "🔥" if improved else "  "
        
        # 格式化时间
        def format_time(seconds):
            if seconds < 60:
                return f"{seconds:.0f}s"
            elif seconds < 3600:
                return f"{seconds//60:.0f}m{seconds%60:.0f}s"
            else:
                return f"{seconds//3600:.0f}h{(seconds%3600)//60:.0f}m"
        
        # 打印详细进度
        print(f"🔄 第{self.current_iter+1:2d}/{self.max_iter}代 | "
              f"进度: {progress:5.1f}% | "
              f"最佳: {self.best_fitness:.6f} {status} | "
              f"用时: {format_time(elapsed)} | "
              f"剩余: {format_time(eta)}")
        
        # 每5代显示详细信息
        if (self.current_iter + 1) % 5 == 0:
            print(f"   📈 平均每代用时: {elapsed/(self.current_iter+1):.1f}s")
            print(f"   🎯 预计完成时间: {time.strftime('%H:%M:%S', time.localtime(time.time() + eta))}")
            print("   " + "-" * 50)
    
    def finish(self):
        """完成优化"""
        total_time = time.time() - self.start_time
        print("\n" + "🎉" + "=" * 58)
        print("✅ HHO优化完成!")
        print(f"⏱️  总用时: {total_time/60:.1f}分钟")
        print(f"🏆 最佳适应度: {self.best_fitness:.6f}")
        print(f"📊 平均每代用时: {total_time/self.max_iter:.1f}秒")
        print("🎉" + "=" * 58)

def create_simple_progress_bar(total, desc="Progress"):
    """创建简单的进度条"""
    try:
        from tqdm import tqdm
        return tqdm(total=total, desc=desc, unit="it")
    except ImportError:
        return ProgressMonitor(total, desc)

def test_progress_monitor():
    """测试进度监控器"""
    print("🧪 测试进度监控器")
    print("=" * 40)
    
    # 测试简单进度条
    print("测试1: 简单进度条")
    pbar = ProgressMonitor(10, "测试进度", "项")
    for i in range(10):
        time.sleep(0.2)
        pbar.update(1)
    pbar.close()
    
    print("\n测试2: HHO进度跟踪器")
    tracker = HHOProgressTracker(5, 10)
    for i in range(5):
        tracker.start_iteration(i)
        time.sleep(0.5)
        fitness = 1.0 - i * 0.1
        improved = i > 0
        tracker.update_fitness(fitness, improved)
    tracker.finish()
    
    print("\n✅ 进度监控器测试完成")

if __name__ == "__main__":
    test_progress_monitor()
