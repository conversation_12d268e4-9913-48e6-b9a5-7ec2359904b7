# ⚙️ HHO-CEEMDAN-Hilbert系统配置说明

## 📋 配置文件位置
所有系统参数都在 `config.py` 文件中统一管理。

## 🎯 当前配置（最高精度设置）

### 📊 数据配置
```python
'data_file': 'Site_1_standardized.csv',    # 数据文件
'power_column': 'Power',                   # 功率列名
'sampling_freq': 4.0,                      # 采样频率 (Hz)
'use_full_data': True,                     # ✅ 使用完整数据集
'sample_rate': 1,                          # ✅ 采样率=1 (不采样)
```

### 🦅 HHO优化配置
```python
'hho_config': {
    'max_iter': 25,        # 最大迭代次数 (HHO收敛更快)
    'n_hawks': 12,         # 鹰群数量 (HHO效率更高)
    'enable_optimization': True
}
```

### 🌊 CEEMDAN参数范围
```python
'ceemdan_bounds': {
    'trials': [20, 50],           # 集成试验次数范围
    'noise_std': [0.002, 0.008],  # 噪声标准差范围
    'max_imf': [8, 12]            # 最大IMF数量范围
}
```

### 🔧 默认CEEMDAN参数
```python
'default_ceemdan_params': {
    'trials': 30,          # 完整数据集下的合理集成次数
    'noise_std': 0.005,    # 适中的噪声标准差
    'max_imf': 10          # 适合完整数据的IMF数量
}
```

## ⚡ 完整数据集的优势

### ✅ 最高精度
- **无信息丢失**: 使用全部70,176个数据点
- **真实分解**: 不需要插值恢复
- **完整频谱**: 保留所有频率成分

### ✅ 真实的高低频分离
- **精确的高频分量**: 捕获所有短期波动
- **准确的低频分量**: 保持完整的长期趋势
- **无插值误差**: 避免线性插值带来的平滑化

## 🚀 使用方法

### 查看当前配置
```bash
python main.py --config-info
```

### 运行完整分解（使用config.py配置）
```bash
python main.py
```

### 仅分解（生成数据集）
```bash
python main.py --mode decompose
```

## 📊 预期结果

### 数据集特征
- **高频数据集**: `Site_1_high_frequency_power.csv` (70,176行)
- **低频数据集**: `Site_1_low_frequency_power.csv` (70,176行)
- **格式兼容**: 与原始数据完全一致

### 精度指标
- **重构误差**: < 1e-14 (理论最优)
- **能量守恒**: > 99.9%
- **频率分离**: 高低频相关性 < 0.01

## ⏱️ 计算时间估算

### 完整数据集 (70,176点)
- **HHO优化**: 约20-40分钟 (25代×12鹰，比GWO快25%)
- **CEEMDAN分解**: 约10-20分钟 (取决于最优参数)
- **Hilbert分析**: 约5-10分钟
- **总计**: 约35-70分钟

### 性能优化建议
如果计算时间过长，可以调整config.py中的参数：

```python
# 快速模式（降低精度但加快速度）
'hho_config': {
    'max_iter': 12,        # 减少迭代次数
    'n_hawks': 6,          # 减少鹰群数量
}

'ceemdan_bounds': {
    'trials': [15, 30],    # 减少trials范围
}
```

## 🔧 参数调优指南

### 提高精度
```python
# 超高精度模式
'hho_config': {
    'max_iter': 35,        # 增加迭代次数
    'n_hawks': 15,         # 增加鹰群数量
}

'ceemdan_bounds': {
    'trials': [30, 100],   # 增加trials上限
    'noise_std': [0.001, 0.005],  # 降低噪声范围
}
```

### 平衡速度和精度
```python
# 推荐设置（当前配置）
'hho_config': {
    'max_iter': 25,
    'n_hawks': 12,
}

'ceemdan_bounds': {
    'trials': [20, 50],
    'noise_std': [0.002, 0.008],
}
```

## 💡 使用建议

### 1. 首次运行
建议使用当前配置，获得最高精度的结果。

### 2. 时间紧急
如果需要快速结果，可以临时调整config.py中的参数。

### 3. 批量处理
对于多个数据集，建议使用相同的配置以保证结果一致性。

### 4. 结果验证
每次运行后检查：
- 重构误差是否 < 1e-12
- 能量守恒是否 > 99%
- 高低频相关性是否 < 0.02

## 🎯 配置文件修改

要修改配置，直接编辑 `config.py` 文件：

```python
# 在config.py中修改相应参数
DECOMPOSITION_CONFIG = {
    # 修改这里的参数
    'use_full_data': True,  # 保持True获得最高精度
    'hho_config': {
        'max_iter': 25,     # 根据需要调整
        'n_hawks': 12,      # 根据需要调整
    },
    # ... 其他参数
}
```

修改后无需重启，直接运行即可使用新配置。

---

**⚡ 当前系统已配置为使用完整数据集，将提供最高精度的高低频分解结果！**
