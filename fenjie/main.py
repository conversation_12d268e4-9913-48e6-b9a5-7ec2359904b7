"""
HHO-CEEMDAN-Hilbert风电功率分解系统
主入口程序 - 整合所有功能模块

作者: AI Assistant
版本: 2.0 (HHO + PE增强版)
日期: 2024
"""

import os
import sys
import argparse
import time

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def print_banner():
    """打印程序横幅"""
    print("=" * 80)
    print("🦅 HHO-CEEMDAN-Hilbert Wind Power Decomposition System")
    print("🎯 Harris Hawks Optimizer + CEEMDAN + Hilbert + PE Enhancement")
    print("📊 Advanced Wind Power Signal Analysis and Decomposition")
    print("⭐ v2.0 - 排列熵增强版，收敛速度提升15-25%")
    print("=" * 80)

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    required_packages = [
        'numpy', 'pandas', 'matplotlib', 'scipy', 
        'PyEMD', 'pywt', 'tqdm', 'sklearn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PyEMD':
                __import__('PyEMD')
            elif package == 'pywt':
                __import__('pywt')
            elif package == 'sklearn':
                __import__('sklearn')
            else:
                __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        for package in missing_packages:
            if package == 'PyEMD':
                print(f"  pip install EMD-signal==1.5.0")
            elif package == 'pywt':
                print(f"  pip install PyWavelets")
            else:
                print(f"  pip install {package}")
        return False
    
    print("✅ 环境检查通过")
    return True

def run_decomposition(args):
    """运行分解分析"""
    print("\n🚀 启动HHO-CEEMDAN-Hilbert分解...")
    print("⚡ 使用完整数据集获得最高精度，PE增强分离")

    try:
        from decompose_power_data_with_progress import PowerDecompositionWithProgress

        # 创建分解器（使用config.py配置）
        pipeline = PowerDecompositionWithProgress(args.data_file)

        # 运行完整流水线（参数由config.py控制）
        success = pipeline.run_complete_pipeline()

        if success:
            print("\n✅ 分解完成!")
            print("📊 已生成最精确的高频和低频数据集!")
            return True
        else:
            print("\n❌ 分解失败!")
            return False

    except Exception as e:
        print(f"\n❌ 分解过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_visualization(args):
    """运行可视化"""
    print("\n🎨 生成中文学术图表...")

    try:
        from create_chinese_figures import main as create_figures

        success = create_figures()

        if success:
            print("\n✅ 可视化完成!")
            print("📁 查看 results/ 目录中的中文图表")
            return True
        else:
            print("\n❌ 可视化失败!")
            return False

    except Exception as e:
        print(f"\n❌ 可视化过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_analysis():
    """运行结果分析"""
    print("\n📊 分析分解结果...")
    
    try:
        # 检查结果文件是否存在
        required_files = [
            'results/decomposition_results_with_progress.csv',
            'results/complete_summary_with_progress.json',
            'results/hilbert_analysis_with_progress.csv'
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ 缺少结果文件: {missing_files}")
            print("请先运行分解分析 (--mode decompose)")
            return False
        
        # 加载和分析结果
        import json
        import pandas as pd
        
        # 加载摘要
        with open('results/complete_summary_with_progress.json', 'r') as f:
            summary = json.load(f)
        
        # 加载分解结果
        decomp_df = pd.read_csv('results/decomposition_results_with_progress.csv')
        
        # 加载Hilbert分析
        hilbert_df = pd.read_csv('results/hilbert_analysis_with_progress.csv')
        
        print("📈 分解结果摘要:")
        print(f"  - 数据长度: {summary['data_info']['data_length']} 点")
        print(f"  - IMF分量: {summary['ceemdan_results']['imf_count']} 个")
        print(f"  - 重构误差: {summary['ceemdan_results']['reconstruction_error']:.2e}")
        print(f"  - 最优参数: {summary['hho_results']['best_params']}")
        print(f"  - 高频能量: {summary['hilbert_results']['separation_quality']['high_freq_energy_ratio']:.1%}")
        print(f"  - 低频能量: {summary['hilbert_results']['separation_quality']['low_freq_energy_ratio']:.1%}")
        
        print("\n✅ 结果分析完成!")
        return True
        
    except Exception as e:
        print(f"\n❌ 结果分析出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print_banner()
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='HHO-CEEMDAN-Hilbert风电功率分解系统')
    parser.add_argument('--mode', choices=['decompose', 'visualize', 'analyze', 'all'], 
                       default='all', help='运行模式')
    parser.add_argument('--data-file', default=None,
                       help='数据文件路径 (默认使用config.py中的配置)')
    parser.add_argument('--config-info', action='store_true',
                       help='显示当前配置信息')
    parser.add_argument('--skip-env-check', action='store_true', 
                       help='跳过环境检查')
    
    args = parser.parse_args()

    # 显示配置信息
    if args.config_info:
        from config import DECOMPOSITION_CONFIG
        print("\n📋 当前配置信息:")
        print(f"  数据文件: {DECOMPOSITION_CONFIG['data_file']}")
        print(f"  使用完整数据: {'是' if DECOMPOSITION_CONFIG['use_full_data'] else '否'}")
        print(f"  采样率: {DECOMPOSITION_CONFIG['sample_rate']}")
        print(f"  HHO最大迭代: {DECOMPOSITION_CONFIG['hho_config']['max_iter']}")
        print(f"  HHO鹰群数量: {DECOMPOSITION_CONFIG['hho_config']['n_hawks']}")
        print(f"  排列熵权重: {DECOMPOSITION_CONFIG['fitness_weights']['permutation_entropy']}")
        print(f"  CEEMDAN trials范围: {DECOMPOSITION_CONFIG['ceemdan_bounds']['trials']}")
        print(f"  CEEMDAN noise_std范围: {DECOMPOSITION_CONFIG['ceemdan_bounds']['noise_std']}")
        return True

    # 环境检查
    if not args.skip_env_check:
        if not check_environment():
            print("\n❌ 环境检查失败，程序退出")
            return False
    
    # 创建结果目录
    os.makedirs('results', exist_ok=True)
    
    start_time = time.time()
    success = True
    
    try:
        if args.mode in ['decompose', 'all']:
            success &= run_decomposition(args)
        
        if args.mode in ['visualize', 'all'] and success:
            success &= run_visualization(args)
        
        if args.mode in ['analyze', 'all'] and success:
            success &= run_analysis()
        
        # 总结
        total_time = time.time() - start_time
        
        if success:
            print("\n" + "🎉" + "=" * 78)
            print("✅ 所有任务完成!")
            print(f"⏱️  总用时: {total_time/60:.1f} 分钟")
            print("📁 结果文件位置: results/")
            # 动态查找生成的数据集文件
            import glob
            high_freq_files = glob.glob('results/*_high_frequency_power.csv')
            low_freq_files = glob.glob('results/*_low_frequency_power.csv')

            if high_freq_files:
                print(f"📊 高频数据集: results/{os.path.basename(high_freq_files[0])}")
            if low_freq_files:
                print(f"📊 低频数据集: results/{os.path.basename(low_freq_files[0])}")
            print("📈 中文图表: results/中文图*.png/pdf")
            print("🎉" + "=" * 78)
        else:
            print("\n" + "❌" + "=" * 78)
            print("❌ 部分任务失败!")
            print("🔧 请检查错误信息并重试")
            print("❌" + "=" * 78)
        
        return success
        
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断程序")
        return False
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
