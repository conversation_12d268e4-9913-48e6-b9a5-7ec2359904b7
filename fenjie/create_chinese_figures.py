"""
中文高质量图表生成器
解决中文字体显示问题，生成色彩鲜明的中文图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from scipy import signal
import json
import os
import warnings
warnings.filterwarnings('ignore')

def setup_chinese_font():
    """设置中文字体"""
    # 尝试多种中文字体
    chinese_fonts = [
        'Microsoft YaHei',  # 微软雅黑
        'SimHei',          # 黑体
        'KaiTi',           # 楷体
        'SimSun',          # 宋体
        'FangSong',        # 仿宋
        'STSong',          # 华文宋体
        'STKaiti',         # 华文楷体
        'STHeiti',         # 华文黑体
    ]
    
    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 找到第一个可用的中文字体
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            break
    
    if selected_font:
        print(f"✅ 使用中文字体: {selected_font}")
        plt.rcParams['font.sans-serif'] = [selected_font]
    else:
        print("⚠️  未找到中文字体，使用默认字体")
        # 使用DejaVu Sans作为备选，它支持一些中文字符
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    
    plt.rcParams['axes.unicode_minus'] = False
    return selected_font

def setup_plot_style():
    """设置绘图样式"""
    plt.rcParams.update({
        'font.size': 14,
        'axes.linewidth': 2,
        'axes.spines.top': False,
        'axes.spines.right': False,
        'xtick.direction': 'in',
        'ytick.direction': 'in',
        'xtick.major.size': 8,
        'ytick.major.size': 8,
        'legend.frameon': True,
        'legend.fancybox': True,
        'legend.edgecolor': 'black',
        'legend.framealpha': 0.95,
        'legend.fontsize': 12,
        'figure.dpi': 150,
        'savefig.dpi': 400,
        'savefig.bbox': 'tight',
        'savefig.pad_inches': 0.3,
        'savefig.facecolor': 'white',
        'figure.facecolor': 'white',
        'axes.facecolor': 'white',
        'grid.alpha': 0.5,
        'grid.linewidth': 1
    })

# 鲜明配色方案
COLORS = {
    'blue': '#1E88E5',      # 鲜明蓝色
    'red': '#E53935',       # 鲜明红色
    'green': '#43A047',     # 鲜明绿色
    'orange': '#FB8C00',    # 鲜明橙色
    'purple': '#8E24AA',    # 鲜明紫色
    'cyan': '#00ACC1',      # 鲜明青色
    'pink': '#D81B60',      # 鲜明粉色
    'lime': '#7CB342',      # 鲜明青柠色
    'amber': '#FFB300',     # 鲜明琥珀色
    'teal': '#00897B'       # 鲜明蓝绿色
}

def load_results():
    """加载分解结果"""
    decomp_df = pd.read_csv('results/decomposition_results_with_progress.csv')
    
    with open('results/complete_summary_with_progress.json', 'r') as f:
        summary = json.load(f)
    
    hilbert_df = pd.read_csv('results/hilbert_analysis_with_progress.csv')
    
    return decomp_df, summary, hilbert_df

def create_chinese_overview_figure(decomp_df, summary):
    """创建中文总览图"""
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('图1. GWO-CEEMDAN-Hilbert风电功率分解结果总览', 
                 fontsize=24, fontweight='bold', y=0.96)
    
    time_hours = np.arange(len(decomp_df)) * 0.25
    
    # (a) 原始信号与重构信号
    ax1 = axes[0, 0]
    ax1.plot(time_hours, decomp_df['Original'], color=COLORS['blue'], 
             linewidth=3, label='原始信号', alpha=0.9)
    ax1.plot(time_hours, decomp_df['Reconstructed'], color=COLORS['red'], 
             linewidth=2.5, linestyle='--', label='重构信号', alpha=0.8)
    ax1.set_xlabel('时间 (小时)', fontsize=16, fontweight='bold')
    ax1.set_ylabel('功率 (MW)', fontsize=16, fontweight='bold')
    ax1.set_title('(a) 原始信号与重构信号对比', fontsize=18, fontweight='bold', pad=20)
    ax1.legend(fontsize=14)
    ax1.grid(True, alpha=0.5)
    
    error = summary['ceemdan_results']['reconstruction_error']
    ax1.text(0.02, 0.98, f'重构误差: {error:.2e}', transform=ax1.transAxes, 
             verticalalignment='top', fontsize=14, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.8', facecolor='lightblue', alpha=0.9, edgecolor='navy'))
    
    # (b) 高低频分离
    ax2 = axes[0, 1]
    ax2.plot(time_hours, decomp_df['High_Freq'], color=COLORS['orange'], 
             linewidth=3, label='高频分量', alpha=0.9)
    ax2.plot(time_hours, decomp_df['Low_Freq'], color=COLORS['green'], 
             linewidth=3, label='低频分量', alpha=0.9)
    ax2.set_xlabel('时间 (小时)', fontsize=16, fontweight='bold')
    ax2.set_ylabel('功率 (MW)', fontsize=16, fontweight='bold')
    ax2.set_title('(b) 高频与低频分量对比', fontsize=18, fontweight='bold', pad=20)
    ax2.legend(fontsize=14)
    ax2.grid(True, alpha=0.5)
    
    high_ratio = summary['hilbert_results']['separation_quality']['high_freq_energy_ratio']
    low_ratio = summary['hilbert_results']['separation_quality']['low_freq_energy_ratio']
    ax2.text(0.02, 0.98, f'高频: {high_ratio:.1%}\n低频: {low_ratio:.1%}', 
             transform=ax2.transAxes, verticalalignment='top', fontsize=14, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.8', facecolor='lightgreen', alpha=0.9, edgecolor='darkgreen'))
    
    # (c) 功率谱密度
    ax3 = axes[1, 0]
    
    def plot_psd(data, label, color, ax):
        # 确保数据是numpy数组
        data_array = np.array(data)
        freqs, psd = signal.welch(data_array, fs=4.0, nperseg=min(256, len(data_array)//4))
        ax.loglog(freqs[1:], psd[1:], color=color, linewidth=3, label=label, alpha=0.9)
    
    plot_psd(decomp_df['Original'], '原始信号', COLORS['blue'], ax3)
    plot_psd(decomp_df['High_Freq'], '高频分量', COLORS['orange'], ax3)
    plot_psd(decomp_df['Low_Freq'], '低频分量', COLORS['green'], ax3)
    
    ax3.set_xlabel('频率 (Hz)', fontsize=16, fontweight='bold')
    ax3.set_ylabel('功率谱密度', fontsize=16, fontweight='bold')
    ax3.set_title('(c) 功率谱密度分析', fontsize=18, fontweight='bold', pad=20)
    ax3.legend(fontsize=14)
    ax3.grid(True, alpha=0.5)
    
    # (d) HHO收敛曲线
    ax4 = axes[1, 1]
    convergence = summary['hho_results']['convergence_curve']

    if convergence and len(convergence) > 0:
        # 有收敛数据时绘制收敛曲线
        iterations = range(1, len(convergence) + 1)

        ax4.plot(iterations, convergence, 'o-', color=COLORS['purple'],
                 linewidth=4, markersize=10, markerfacecolor='white',
                 markeredgewidth=3, markeredgecolor=COLORS['purple'])
        ax4.set_xlabel('迭代次数', fontsize=16, fontweight='bold')
        ax4.set_ylabel('适应度值', fontsize=16, fontweight='bold')
        ax4.set_title('(d) HHO收敛曲线', fontsize=18, fontweight='bold', pad=20)
        ax4.grid(True, alpha=0.5)

        best_fitness = min(convergence)
        best_iter = convergence.index(best_fitness) + 1
        ax4.annotate(f'最优值: {best_fitness:.6f}',
                    xy=(best_iter, best_fitness), xytext=(best_iter+2, best_fitness+0.0001),
                    arrowprops=dict(arrowstyle='->', color=COLORS['red'], lw=3),
                    bbox=dict(boxstyle='round,pad=0.8', facecolor='yellow', alpha=0.9, edgecolor='orange'),
                    fontsize=14, fontweight='bold')
    else:
        # 没有收敛数据时显示说明
        ax4.text(0.5, 0.5, '未进行HHO优化\n使用默认参数',
                ha='center', va='center', transform=ax4.transAxes,
                fontsize=16, fontweight='bold', color=COLORS['purple'],
                bbox=dict(boxstyle='round,pad=1', facecolor='lightgray', alpha=0.8))
        ax4.set_xlabel('迭代次数', fontsize=16, fontweight='bold')
        ax4.set_ylabel('适应度值', fontsize=16, fontweight='bold')
        ax4.set_title('(d) HHO收敛曲线', fontsize=18, fontweight='bold', pad=20)
        ax4.grid(True, alpha=0.5)
    
    plt.tight_layout(pad=3.0)
    plt.savefig('results/中文图1_分解结果总览.png', dpi=400, bbox_inches='tight', facecolor='white')
    plt.savefig('results/中文图1_分解结果总览.pdf', dpi=400, bbox_inches='tight', facecolor='white')
    print("✅ 中文图1: 分解结果总览 已保存")
    return fig

def create_chinese_imf_figure(decomp_df, summary):
    """创建中文IMF分解图"""
    imf_count = summary['ceemdan_results']['imf_count']
    
    fig, axes = plt.subplots(imf_count + 1, 1, figsize=(18, 4 * (imf_count + 1)))
    fig.suptitle('图2. CEEMDAN分解：IMF分量展示', 
                 fontsize=24, fontweight='bold', y=0.98)
    
    time_hours = np.arange(len(decomp_df)) * 0.25
    
    # 原始信号
    axes[0].plot(time_hours, decomp_df['Original'], color=COLORS['blue'], linewidth=3)
    axes[0].set_ylabel('原始信号\n(MW)', fontsize=16, fontweight='bold')
    axes[0].set_title('原始风电功率信号', fontsize=18, fontweight='bold', pad=20)
    axes[0].grid(True, alpha=0.5)
    
    # IMF分量
    color_list = list(COLORS.values())
    
    for i in range(imf_count):
        imf_col = f'IMF_{i+1}'
        if imf_col in decomp_df.columns:
            color = color_list[i % len(color_list)]
            axes[i+1].plot(time_hours, decomp_df[imf_col], 
                          color=color, linewidth=3, alpha=0.9)
            axes[i+1].set_ylabel(f'IMF {i+1}\n(MW)', fontsize=16, fontweight='bold')
            axes[i+1].grid(True, alpha=0.5)
            
            # 添加频率信息
            try:
                hilbert_df = pd.read_csv('results/hilbert_analysis_with_progress.csv')
                freq = hilbert_df.iloc[i]['mean_frequency']
                energy_ratio = hilbert_df.iloc[i]['energy_ratio']
                axes[i+1].text(0.02, 0.8, f'平均频率 = {freq:.4f} Hz\n能量占比 = {energy_ratio:.1%}', 
                              transform=axes[i+1].transAxes,
                              bbox=dict(boxstyle='round,pad=0.8', facecolor='lightcyan', 
                                       alpha=0.9, edgecolor=color),
                              fontsize=14, fontweight='bold')
            except:
                pass
    
    axes[-1].set_xlabel('时间 (小时)', fontsize=16, fontweight='bold')
    
    plt.tight_layout(pad=3.0)
    plt.savefig('results/中文图2_IMF分量分解.png', dpi=400, bbox_inches='tight', facecolor='white')
    plt.savefig('results/中文图2_IMF分量分解.pdf', dpi=400, bbox_inches='tight', facecolor='white')
    print("✅ 中文图2: IMF分量分解 已保存")
    return fig

def create_chinese_frequency_analysis(decomp_df, hilbert_df):
    """创建中文频域分析图"""
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('图3. 频域分析', 
                 fontsize=24, fontweight='bold', y=0.96)
    
    # (a) 功率谱密度对比
    ax1 = axes[0, 0]
    
    signals = {
        '原始信号': decomp_df['Original'],
        '高频分量': decomp_df['High_Freq'],
        '低频分量': decomp_df['Low_Freq']
    }
    
    colors = [COLORS['blue'], COLORS['orange'], COLORS['green']]
    
    for i, (name, data) in enumerate(signals.items()):
        # 确保数据是numpy数组
        data_array = np.array(data)
        freqs, psd = signal.welch(data_array, fs=4.0, nperseg=min(256, len(data_array)//4))
        ax1.loglog(freqs[1:], psd[1:], color=colors[i], linewidth=3,
                  label=name, alpha=0.9)
    
    ax1.set_xlabel('频率 (Hz)', fontsize=16, fontweight='bold')
    ax1.set_ylabel('功率谱密度', fontsize=16, fontweight='bold')
    ax1.set_title('(a) 功率谱密度对比', fontsize=18, fontweight='bold', pad=20)
    ax1.legend(fontsize=14)
    ax1.grid(True, alpha=0.5)
    
    # (b) IMF频率分布
    ax2 = axes[0, 1]
    
    frequencies = hilbert_df['mean_frequency']
    energy_ratios = hilbert_df['energy_ratio']
    
    color_list = list(COLORS.values())
    bar_colors = [color_list[i % len(color_list)] for i in range(len(hilbert_df))]
    
    bars = ax2.bar(range(1, len(hilbert_df)+1), frequencies, 
                   color=bar_colors, alpha=0.9, edgecolor='black', linewidth=2)
    
    ax2.set_xlabel('IMF序号', fontsize=16, fontweight='bold')
    ax2.set_ylabel('平均频率 (Hz)', fontsize=16, fontweight='bold')
    ax2.set_title('(b) IMF频率分布', fontsize=18, fontweight='bold', pad=20)
    ax2.grid(True, alpha=0.5)
    
    # 添加能量比例标签
    for i, (bar, energy) in enumerate(zip(bars, energy_ratios)):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                f'{energy:.1%}', ha='center', va='bottom', 
                fontsize=12, fontweight='bold')
    
    # (c) 频率-能量散点图
    ax3 = axes[1, 0]
    
    scatter = ax3.scatter(frequencies, energy_ratios, 
                         c=bar_colors, s=400, alpha=0.9, 
                         edgecolors='black', linewidth=2)
    
    for i, (freq, energy) in enumerate(zip(frequencies, energy_ratios)):
        ax3.annotate(f'IMF{i+1}', (freq, energy), 
                    xytext=(10, 10), textcoords='offset points',
                    fontsize=14, fontweight='bold', 
                    bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.8))
    
    ax3.set_xlabel('平均频率 (Hz)', fontsize=16, fontweight='bold')
    ax3.set_ylabel('能量占比', fontsize=16, fontweight='bold')
    ax3.set_title('(c) 频率-能量分布', fontsize=18, fontweight='bold', pad=20)
    ax3.grid(True, alpha=0.5)
    
    # (d) 累积能量谱
    ax4 = axes[1, 1]
    
    cumulative_energy = np.cumsum(energy_ratios)
    
    ax4.plot(range(1, len(hilbert_df)+1), cumulative_energy, 
            'o-', linewidth=4, markersize=12, color=COLORS['cyan'],
            markerfacecolor='white', markeredgewidth=3)
    ax4.fill_between(range(1, len(hilbert_df)+1), cumulative_energy, 
                    alpha=0.3, color=COLORS['cyan'])
    
    ax4.axhline(y=0.9, color=COLORS['red'], linestyle='--', linewidth=3, 
               label='90%能量阈值')
    
    ax4.set_xlabel('IMF序号', fontsize=16, fontweight='bold')
    ax4.set_ylabel('累积能量占比', fontsize=16, fontweight='bold')
    ax4.set_title('(d) 累积能量谱', fontsize=18, fontweight='bold', pad=20)
    ax4.legend(fontsize=14)
    ax4.grid(True, alpha=0.5)
    ax4.set_ylim(0, 1.1)
    
    plt.tight_layout(pad=3.0)
    plt.savefig('results/中文图3_频域分析.png', dpi=400, bbox_inches='tight', facecolor='white')
    plt.savefig('results/中文图3_频域分析.pdf', dpi=400, bbox_inches='tight', facecolor='white')
    print("✅ 中文图3: 频域分析 已保存")
    return fig

def main():
    """主函数"""
    print("🎨 生成中文高质量图表")
    print("=" * 60)
    
    # 设置中文字体
    font_name = setup_chinese_font()
    setup_plot_style()
    
    try:
        # 加载数据
        decomp_df, summary, hilbert_df = load_results()
        print(f"✅ 数据加载成功")
        
        # 生成中文图表
        print("\n📈 生成中文总览图...")
        create_chinese_overview_figure(decomp_df, summary)
        
        print("📈 生成中文IMF分解图...")
        create_chinese_imf_figure(decomp_df, summary)
        
        print("📈 生成中文频域分析图...")
        create_chinese_frequency_analysis(decomp_df, hilbert_df)
        
        print("\n🎉 所有中文图表生成完成!")
        print("📁 文件保存在 results/ 目录")
        print("   - 中文图1_分解结果总览.png/pdf")
        print("   - 中文图2_IMF分量分解.png/pdf") 
        print("   - 中文图3_频域分析.png/pdf")
        
        if font_name:
            print(f"✅ 使用字体: {font_name}")
        else:
            print("⚠️  如果中文显示为方框，请安装中文字体")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
