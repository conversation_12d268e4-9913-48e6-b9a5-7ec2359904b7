"""
快速测试HHO-CEEMDAN-Hilbert系统
使用小数据集验证功能
"""

import numpy as np
import pandas as pd
from decompose_power_data_with_progress import PowerDecompositionWithProgress

def create_test_data():
    """创建测试数据"""
    print("📊 创建测试数据...")
    
    # 生成1000个数据点的测试信号
    t = np.linspace(0, 100, 1000)
    
    # 高频分量
    high_freq = 0.3 * np.sin(2 * np.pi * 0.5 * t) + 0.1 * np.random.randn(1000)
    
    # 低频分量
    low_freq = 0.8 * np.sin(2 * np.pi * 0.05 * t) + 0.2 * t / 100
    
    # 合成信号
    power_signal = high_freq + low_freq + 5.0
    
    # 创建DataFrame（使用正确的列名）
    test_data = pd.DataFrame({
        'Power (MW)': power_signal
    })
    
    # 保存测试数据
    test_data.to_csv('test_small_data.csv', index=False)
    print(f"✅ 测试数据已保存: test_small_data.csv")
    print(f"📊 数据长度: {len(test_data)}")
    print(f"📊 功率范围: [{test_data['Power (MW)'].min():.2f}, {test_data['Power (MW)'].max():.2f}]")
    
    return 'test_small_data.csv'

def test_hho_system():
    """测试HHO系统"""
    print("🦅 测试HHO-CEEMDAN-Hilbert系统")
    print("=" * 50)
    
    # 创建测试数据
    test_file = create_test_data()
    
    try:
        # 创建分解器
        print("\n🔧 创建分解器...")
        decomposer = PowerDecompositionWithProgress(test_file)
        
        # 临时修改配置为快速模式
        print("⚡ 设置快速模式...")
        decomposer.config['hho_config']['max_iter'] = 3
        decomposer.config['hho_config']['n_hawks'] = 3
        decomposer.use_full_data = True
        decomposer.sample_rate = 1
        
        print(f"📊 HHO配置: {decomposer.config['hho_config']['max_iter']}代 × {decomposer.config['hho_config']['n_hawks']}鹰")
        
        # 运行分解
        print("\n🚀 开始分解...")
        success = decomposer.run_complete_pipeline()
        
        if success:
            print("\n✅ 测试成功!")
            print("🎉 HHO-CEEMDAN-Hilbert系统工作正常")
            
            # 检查结果
            if hasattr(decomposer, 'hho_results'):
                print(f"🦅 HHO结果: {decomposer.hho_results['best_params']}")
            
            return True
        else:
            print("\n❌ 测试失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试文件
        import os
        if os.path.exists('test_small_data.csv'):
            os.remove('test_small_data.csv')
            print("🧹 测试文件已清理")

if __name__ == "__main__":
    success = test_hho_system()
    if success:
        print("\n🎉 HHO系统验证成功!")
        print("✅ 可以安全使用完整数据集")
    else:
        print("\n❌ HHO系统验证失败")
        print("🔧 需要进一步调试")
