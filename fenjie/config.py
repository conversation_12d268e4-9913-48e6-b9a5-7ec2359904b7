"""
HHO-CEEMDAN-Hilbert分解配置文件
集成排列熵(PE)增强分析
"""

import os

# 基本配置
DECOMPOSITION_CONFIG = {
    # 数据配置
    'data_file': 'Wind farm site 6 (Nominal capacity-96MW).csv',
    'power_column': 'Power (MW)',
    'sampling_freq': 4.0,  # Hz, 15分钟间隔 = 4次/小时
    'use_full_data': True,  # 使用完整数据集（最高精度）
    'sample_rate': 1,       # 采样率（1=不采样，使用全部数据）
    
    # HHO优化配置 (完整数据集高精度设置)
    'hho_config': {
        'max_iter': 10,        # 最大迭代次数 (HHO收敛更快，可减少迭代)
        'n_hawks': 5,         # 鹰群数量 (HHO效率更高，可减少个体数)
        'enable_optimization': True     # 是否启用参数优化
    },
    
    # CEEMDAN参数范围 (完整数据集优化设置)
    'ceemdan_bounds': {
        'trials': [20, 50],       # 集成试验次数范围 (完整数据集下适中)
        'noise_std': [0.002, 0.008],  # 噪声标准差范围 (精确范围)
        'max_imf': [8, 12]        # 最大IMF数量范围 (适合完整数据)
    },

    # 默认CEEMDAN参数（不优化时使用，完整数据集高质量设置）
    'default_ceemdan_params': {
        'trials': 30,          # 完整数据集下的合理集成次数
        'noise_std': 0.008004281584096305,    # 适中的噪声标准差
        'max_imf': 7          # 适合完整数据的IMF数量
    },
    
    # 频率分割配置
    'frequency_config': {
        'cutoff_hours': 6,        # 高低频分割时间尺度（小时）
        'use_hilbert_refinement': True  # 是否使用Hilbert变换优化分组
    },
    
    # 适应度函数权重 (针对"分而治之"优化 - 增强版)
    'fitness_weights': {
        'reconstruction_error': 0.20,    # 重构误差权重 (基础质量保证)
        'orthogonality': 0.15,           # 正交性权重 (IMF独立性)
        'mode_mixing': 0.15,             # 模态混叠权重 (频率纯净度)
        'frequency_separation': 0.15,    # 频率分离度权重 (频域分离)
        'feature_separability': 0.15,    # 特征可分性权重 (关键：支持分而治之)
        'permutation_entropy': 0.15,     # 排列熵权重 (复杂度分离 - 新增)
        'energy_distribution': 0.05      # 能量分布权重 (分解合理性)
    },

    # 排列熵配置 (新增)
    'permutation_entropy_config': {
        'order': 3,                      # PE计算的嵌入维数 (推荐3-7)
        'delay': 1,                      # 时间延迟 (通常为1)
        'normalize': True,               # 是否归一化PE值
        'relative_variance_threshold': 0.01,  # 相对方差阈值
        'enable_weighted_pe': True,      # 是否启用加权排列熵
        'complexity_threshold': 0.5      # 复杂度分离阈值
    },
    
    # 可视化配置
    'visualization_config': {
        'figure_size': (15, 10),
        'dpi': 300,
        'max_imfs_display': 8,
        'save_figures': True
    },
    
    # 输出配置
    'output_config': {
        'save_decomposed_data': True,
        'save_decomposition_object': True,
        'generate_report': True,
        'results_dir': 'results',
        'figures_dir': 'results/figures'
    }
}

# 颜色配置
COLORS = {
    'primary': '#1E88E5',      # 主色调 - 蓝色
    'secondary': '#E91E63',    # 次色调 - 粉红色
    'accent': '#FF9800',       # 强调色 - 橙色
    'success': '#4CAF50',      # 成功色 - 绿色
    'info': '#00BCD4',         # 信息色 - 青色
    'warning': '#FFC107',      # 警告色 - 黄色
    'light': '#F5F5F5',        # 浅色
    'dark': '#212121'          # 深色
}

# 文件路径配置
def get_paths(base_dir: str = None) -> dict:
    """
    获取文件路径配置
    
    Args:
        base_dir: 基础目录，默认为当前文件所在目录
        
    Returns:
        路径字典
    """
    if base_dir is None:
        base_dir = os.path.dirname(os.path.abspath(__file__))
    
    return {
        'base_dir': base_dir,
        'data_file': os.path.join(base_dir, DECOMPOSITION_CONFIG['data_file']),
        'results_dir': os.path.join(base_dir, DECOMPOSITION_CONFIG['output_config']['results_dir']),
        'figures_dir': os.path.join(base_dir, DECOMPOSITION_CONFIG['output_config']['figures_dir']),
        'decomposed_data': os.path.join(base_dir, 'results', 'decomposed_power_data.csv'),
        'decomposition_object': os.path.join(base_dir, 'results', 'decomposition_results.pkl'),
        'report_file': os.path.join(base_dir, 'results', 'decomposition_report.txt')
    }

# 验证配置
def validate_config() -> bool:
    """
    验证配置参数的有效性
    
    Returns:
        配置是否有效
    """
    config = DECOMPOSITION_CONFIG
    
    # 检查HHO参数
    if config['hho_config']['max_iter'] <= 0:
        print("错误: HHO最大迭代次数必须大于0")
        return False

    if config['hho_config']['n_hawks'] <= 0:
        print("错误: HHO鹰群数量必须大于0")
        return False
    
    # 检查CEEMDAN参数范围
    bounds = config['ceemdan_bounds']
    if bounds['trials'][0] >= bounds['trials'][1]:
        print("错误: trials参数范围无效")
        return False
    
    if bounds['noise_std'][0] >= bounds['noise_std'][1]:
        print("错误: noise_std参数范围无效")
        return False
    
    if bounds['max_imf'][0] >= bounds['max_imf'][1]:
        print("错误: max_imf参数范围无效")
        return False
    
    # 检查权重和
    weights = config['fitness_weights']
    total_weight = sum(weights.values())
    if abs(total_weight - 1.0) > 0.01:
        print(f"警告: 适应度函数权重和为 {total_weight:.3f}，建议调整为1.0")
    
    return True

# 打印配置信息
def print_config():
    """
    打印当前配置信息
    """
    print("=" * 60)
    print("HHO-CEEMDAN-Hilbert分解配置 (PE增强版)")
    print("=" * 60)
    
    config = DECOMPOSITION_CONFIG
    
    print(f"数据文件: {config['data_file']}")
    print(f"采样频率: {config['sampling_freq']} Hz")
    print(f"高低频分割: {config['frequency_config']['cutoff_hours']} 小时")
    
    print(f"\nHHO优化配置:")
    print(f"  启用优化: {config['hho_config']['enable_optimization']}")
    print(f"  最大迭代: {config['hho_config']['max_iter']}")
    print(f"  鹰群数量: {config['hho_config']['n_hawks']}")

    print(f"\n排列熵配置:")
    pe_config = config['permutation_entropy_config']
    print(f"  嵌入维数: {pe_config['order']}")
    print(f"  时间延迟: {pe_config['delay']}")
    print(f"  启用加权PE: {pe_config['enable_weighted_pe']}")
    print(f"  复杂度阈值: {pe_config['complexity_threshold']}")
    
    print(f"\nCEEMDAN参数范围:")
    for param, bounds in config['ceemdan_bounds'].items():
        print(f"  {param}: [{bounds[0]}, {bounds[1]}]")
    
    print(f"\n适应度权重:")
    for weight_name, weight_value in config['fitness_weights'].items():
        print(f"  {weight_name}: {weight_value}")
    
    print("=" * 60)

if __name__ == "__main__":
    # 验证和打印配置
    if validate_config():
        print_config()
    else:
        print("配置验证失败!")
