"""
HHO-CEEMDAN-Hilbert频率分解模块
专门用于风电功率数据的高频/低频分解
"""

# 设置环境变量解决OpenMP冲突
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

# 第三方库导入 (完全静默)
PYEMD_ERROR = None
PYWT_ERROR = None

try:
    from PyEMD import CEEMDAN, EEMD
    HAS_PYEMD = True
except ImportError as e:
    HAS_PYEMD = False
    PYEMD_ERROR = str(e)

try:
    import pywt
    HAS_PYWT = True
except ImportError as e:
    HAS_PYWT = False
    PYWT_ERROR = str(e)

from scipy import signal, stats
from scipy.fft import fft, fftfreq
from scipy.signal import hilbert
import joblib
import os
import sys

# 导入排列熵模块
try:
    from permutation_entropy import (
        permutation_entropy,
        weighted_permutation_entropy,
        calculate_pe_based_complexity_score,
        pe_enhanced_fitness_component
    )
    HAS_PE_MODULE = True
    print("✓ 排列熵模块导入成功")
except ImportError as e:
    HAS_PE_MODULE = False
    print(f"⚠️  排列熵模块导入失败: {e}")
    print("将使用传统方法进行复杂度分析")

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

# 导入项目配置
try:
    from pedict.src.utils.config import ACADEMIC_COLORS, setup_matplotlib
    setup_matplotlib()
except ImportError:
    # 如果无法导入配置，使用默认设置
    ACADEMIC_COLORS = {
        'primary': '#1E88E5',
        'secondary': '#E91E63', 
        'accent': '#FF9800',
        'success': '#4CAF50',
        'info': '#00BCD4',
        'warning': '#FFC107',
        'light': '#F5F5F5',
        'dark': '#212121'
    }


class FrequencyDecomposer:
    """
    HHO-CEEMDAN-Hilbert频率分解器
    专门用于风电功率数据的高频/低频分解
    集成排列熵(PE)增强分析
    """
    
    def __init__(self, sampling_freq: float = 4.0):
        """
        初始化频率分解器

        Args:
            sampling_freq: 采样频率 (Hz)，15分钟间隔 = 4次/小时
        """
        # 检查必要的依赖库
        self._check_dependencies()

        self.sampling_freq = sampling_freq
        self.original_data = None
        self.decomposition_results = {}
        self.frequency_analysis = {}
        self.hho_results = {}

        # 分解参数
        self.optimal_params = None

    def _check_dependencies(self):
        """检查依赖库"""
        if HAS_PYEMD:
            print("✓ PyEMD库导入成功")
        else:
            raise ImportError(f"PyEMD库导入失败: {PYEMD_ERROR}")

        if HAS_PYWT:
            print("✓ PyWavelets库导入成功")
        else:
            raise ImportError(f"PyWavelets库导入失败: {PYWT_ERROR}")
        
    def load_data(self, data: Union[pd.DataFrame, np.ndarray, str], 
                  power_column: str = 'Power') -> np.ndarray:
        """
        加载功率数据
        
        Args:
            data: 数据源（DataFrame、数组或文件路径）
            power_column: 功率列名
            
        Returns:
            功率数据数组
        """
        if isinstance(data, str):
            # 从文件加载
            df = pd.read_csv(data)
            self.original_data = df[power_column].values
        elif isinstance(data, pd.DataFrame):
            self.original_data = data[power_column].values
        elif isinstance(data, np.ndarray):
            self.original_data = data
        else:
            raise ValueError("不支持的数据类型")
            
        print(f"数据加载完成，长度: {len(self.original_data)}")
        print(f"数据范围: [{self.original_data.min():.4f}, {self.original_data.max():.4f}]")
        return self.original_data
    
    def analyze_frequency_spectrum(self, data: np.ndarray = None) -> Dict:
        """
        分析频率谱，为分解提供参考
        
        Args:
            data: 要分析的数据，默认使用原始数据
            
        Returns:
            频率分析结果
        """
        if data is None:
            data = self.original_data
            
        if data is None:
            raise ValueError("请先加载数据")
        
        print("开始频率谱分析...")
        
        # FFT分析
        n = len(data)
        fft_values = fft(data)
        frequencies = fftfreq(n, 1/self.sampling_freq)
        
        # 只取正频率部分
        positive_freq_idx = frequencies > 0
        frequencies = frequencies[positive_freq_idx]
        power_spectrum = np.abs(fft_values[positive_freq_idx])**2
        
        # 找到主要频率成分
        sorted_indices = np.argsort(power_spectrum)[::-1]
        dominant_freqs = frequencies[sorted_indices[:10]]
        dominant_powers = power_spectrum[sorted_indices[:10]]
        
        # 计算频率统计信息
        total_power = np.sum(power_spectrum)
        cumulative_power = np.cumsum(power_spectrum[sorted_indices])
        power_90_idx = np.where(cumulative_power >= 0.9 * total_power)[0][0]
        
        self.frequency_analysis = {
            'frequencies': frequencies,
            'power_spectrum': power_spectrum,
            'dominant_frequencies': dominant_freqs,
            'dominant_powers': dominant_powers,
            'nyquist_freq': self.sampling_freq / 2,
            'total_power': total_power,
            'power_90_freq': frequencies[sorted_indices[power_90_idx]]
        }
        
        print("频率谱分析完成")
        print(f"主要频率成分 (Hz): {dominant_freqs[:5]}")
        print(f"90%能量集中在前{power_90_idx+1}个频率成分中")
        
        return self.frequency_analysis
    
    def fitness_function(self, params: np.ndarray, data: np.ndarray) -> float:
        """
        HHO优化的适应度函数

        Args:
            params: CEEMDAN参数 [trials, noise_std, max_imf]
            data: 输入数据

        Returns:
            适应度值（越小越好）
        """
        try:
            # 参数解码
            trials = int(params[0])
            noise_std = params[1]
            max_imf = int(params[2])
            
            # 参数边界检查
            trials = max(10, min(trials, 200))
            noise_std = max(0.001, min(noise_std, 0.02))
            max_imf = max(4, min(max_imf, 12))
            
            # 执行CEEMDAN分解
            ceemdan = CEEMDAN(trials=trials, noise_width=noise_std)
            imfs = ceemdan.ceemdan(data, max_imf=max_imf)
            
            # 计算评估指标
            
            # 1. 重构误差
            reconstructed = np.sum(imfs, axis=0)
            reconstruction_error = np.mean((data - reconstructed)**2)
            
            # 2. 正交性指标
            orthogonality = 0
            n_imfs = len(imfs) - 1  # 排除残差
            for i in range(n_imfs):
                for j in range(i+1, n_imfs):
                    correlation = np.abs(np.corrcoef(imfs[i], imfs[j])[0, 1])
                    orthogonality += correlation
            
            if n_imfs > 1:
                orthogonality /= (n_imfs * (n_imfs - 1) / 2)
            
            # 3. 模态混叠度（基于频率分离）
            mode_mixing = 0
            for i in range(len(imfs) - 1):
                # 计算IMF的主导频率
                fft_imf = fft(imfs[i])
                freqs = fftfreq(len(imfs[i]), 1/self.sampling_freq)
                power_spec = np.abs(fft_imf)**2
                
                # 找到最大功率对应的频率
                max_power_idx = np.argmax(power_spec[1:len(power_spec)//2]) + 1
                dominant_freq = abs(freqs[max_power_idx])
                
                # 检查频率单调性
                if i > 0:
                    prev_fft = fft(imfs[i-1])
                    prev_power_spec = np.abs(prev_fft)**2
                    prev_max_idx = np.argmax(prev_power_spec[1:len(prev_power_spec)//2]) + 1
                    prev_dominant_freq = abs(freqs[prev_max_idx])
                    
                    if dominant_freq >= prev_dominant_freq:
                        mode_mixing += 1
            
            mode_mixing /= max(1, len(imfs) - 2)
            
            # 4. 频率分离度
            freq_separation = 0
            cutoff_freq = 1.0 / (6 * 3600) * self.sampling_freq  # 6小时对应的频率
            
            high_freq_energy = 0
            low_freq_energy = 0
            
            for i, imf in enumerate(imfs[:-1]):  # 排除残差
                fft_imf = fft(imf)
                freqs = fftfreq(len(imf), 1/self.sampling_freq)
                power_spec = np.abs(fft_imf)**2
                
                max_power_idx = np.argmax(power_spec[1:len(power_spec)//2]) + 1
                dominant_freq = abs(freqs[max_power_idx])
                
                energy = np.sum(power_spec)
                if dominant_freq > cutoff_freq:
                    high_freq_energy += energy
                else:
                    low_freq_energy += energy
            
            total_energy = high_freq_energy + low_freq_energy
            if total_energy > 0:
                freq_separation = min(high_freq_energy, low_freq_energy) / total_energy
            
            # 5. 特征可分性 (新增 - 为"分而治之"优化)
            feature_separability = self.calculate_feature_separability(imfs)

            # 6. 能量分布合理性 (新增)
            energy_distribution = self.calculate_energy_distribution_score(imfs)

            # 7. 排列熵复杂度分离 (新增 - PE增强)
            permutation_entropy_score = 0.0
            if HAS_PE_MODULE:
                try:
                    from config import DECOMPOSITION_CONFIG
                    pe_config = DECOMPOSITION_CONFIG.get('permutation_entropy_config', {
                        'order': 3, 'delay': 1, 'normalize': True,
                        'enable_weighted_pe': True, 'complexity_threshold': 0.5
                    })

                    permutation_entropy_score = pe_enhanced_fitness_component(
                        imfs, cutoff_freq, self.sampling_freq, pe_config
                    )
                except Exception as e:
                    print(f"⚠️  排列熵计算失败: {e}")
                    permutation_entropy_score = 0.0

            # 综合适应度函数（使用配置文件权重）
            try:
                from config import DECOMPOSITION_CONFIG
                weights = DECOMPOSITION_CONFIG['fitness_weights']
            except:
                # 默认权重 (包含PE)
                weights = {
                    'reconstruction_error': 0.20,
                    'orthogonality': 0.15,
                    'mode_mixing': 0.15,
                    'frequency_separation': 0.15,
                    'feature_separability': 0.15,
                    'permutation_entropy': 0.15,
                    'energy_distribution': 0.05
                }

            fitness = (weights['reconstruction_error'] * reconstruction_error +
                      weights['orthogonality'] * orthogonality +
                      weights['mode_mixing'] * mode_mixing +
                      weights['frequency_separation'] * (1 - freq_separation) +
                      weights['feature_separability'] * (1 - feature_separability) +
                      weights.get('permutation_entropy', 0.0) * (1 - permutation_entropy_score) +
                      weights['energy_distribution'] * (1 - energy_distribution))

            return fitness
            
        except Exception as e:
            # 如果分解失败，返回很大的适应度值
            print(f"分解失败: {e}")
            return 1e6

    def calculate_feature_separability(self, imfs: List[np.ndarray]) -> float:
        """
        计算IMF的特征可分性 - 为"分而治之"优化

        Args:
            imfs: IMF分量列表

        Returns:
            特征可分性得分 (0-1, 越高越好)
        """
        try:
            if len(imfs) < 3:
                return 0.0

            # 计算每个IMF的特征
            features = []
            for imf in imfs[:-1]:  # 排除残差
                # 变异系数
                variability = np.std(imf) / (np.mean(np.abs(imf)) + 1e-8)

                # 周期性强度 (自相关函数的最大值)
                autocorr = np.correlate(imf, imf, mode='full')
                autocorr = autocorr[autocorr.size // 2:]
                autocorr = autocorr / autocorr[0]
                periodicity = np.max(autocorr[1:min(len(autocorr)//4, 100)])

                # 趋势强度 (线性拟合的R²)
                x = np.arange(len(imf))
                slope, intercept, r_value, _, _ = stats.linregress(x, imf)
                trend_strength = r_value ** 2

                # 频率集中度
                fft_imf = fft(imf)
                power_spec = np.abs(fft_imf)**2
                power_spec = power_spec[:len(power_spec)//2]
                power_spec = power_spec / np.sum(power_spec)
                freq_concentration = np.sum(power_spec**2)  # 频谱熵的倒数

                features.append([variability, periodicity, trend_strength, freq_concentration])

            features = np.array(features)

            # 计算特征空间中的分离度
            # 使用聚类分析评估高低频IMF的可分性
            cutoff_freq = 1.0 / (6 * 3600) * self.sampling_freq

            high_freq_features = []
            low_freq_features = []

            for i, imf in enumerate(imfs[:-1]):
                fft_imf = fft(imf)
                freqs = fftfreq(len(imf), 1/self.sampling_freq)
                power_spec = np.abs(fft_imf)**2
                max_power_idx = np.argmax(power_spec[1:len(power_spec)//2]) + 1
                dominant_freq = abs(freqs[max_power_idx])

                if dominant_freq > cutoff_freq:
                    high_freq_features.append(features[i])
                else:
                    low_freq_features.append(features[i])

            if len(high_freq_features) == 0 or len(low_freq_features) == 0:
                return 0.0

            # 计算类间距离与类内距离的比值
            high_freq_features = np.array(high_freq_features)
            low_freq_features = np.array(low_freq_features)

            # 类中心
            high_center = np.mean(high_freq_features, axis=0)
            low_center = np.mean(low_freq_features, axis=0)

            # 类间距离
            inter_class_distance = np.linalg.norm(high_center - low_center)

            # 类内距离
            high_intra_distance = np.mean([np.linalg.norm(f - high_center) for f in high_freq_features])
            low_intra_distance = np.mean([np.linalg.norm(f - low_center) for f in low_freq_features])
            intra_class_distance = (high_intra_distance + low_intra_distance) / 2

            # 特征可分性得分
            separability = inter_class_distance / (intra_class_distance + 1e-8)

            # 归一化到0-1范围
            separability = min(1.0, separability / 10.0)

            return separability

        except Exception as e:
            print(f"特征可分性计算失败: {e}")
            return 0.0

    def calculate_energy_distribution_score(self, imfs: List[np.ndarray]) -> float:
        """
        计算能量分布合理性得分

        Args:
            imfs: IMF分量列表

        Returns:
            能量分布得分 (0-1, 越高越好)
        """
        try:
            if len(imfs) < 2:
                return 0.0

            # 计算每个IMF的能量
            energies = []
            for imf in imfs:
                energy = np.sum(imf**2)
                energies.append(energy)

            energies = np.array(energies)
            total_energy = np.sum(energies)

            if total_energy == 0:
                return 0.0

            # 能量比例
            energy_ratios = energies / total_energy

            # 期望的能量分布：高频IMF能量递减，低频IMF包含主要能量
            # 计算能量分布的合理性

            # 1. 避免能量过于集中在单个IMF
            max_energy_ratio = np.max(energy_ratios[:-1])  # 排除残差
            concentration_penalty = max(0, max_energy_ratio - 0.6)  # 单个IMF能量不应超过60%

            # 2. 残差应包含适量能量（趋势信息）
            residue_energy_ratio = energy_ratios[-1]
            residue_score = 1.0 - abs(residue_energy_ratio - 0.3)  # 期望残差占30%左右
            residue_score = max(0, residue_score)

            # 3. IMF能量应大致递减
            imf_energies = energy_ratios[:-1]  # 排除残差
            if len(imf_energies) > 1:
                energy_trend_score = 0
                for i in range(len(imf_energies) - 1):
                    if imf_energies[i] >= imf_energies[i+1]:
                        energy_trend_score += 1
                energy_trend_score /= (len(imf_energies) - 1)
            else:
                energy_trend_score = 1.0

            # 综合得分
            distribution_score = (0.4 * (1 - concentration_penalty) +
                                0.3 * residue_score +
                                0.3 * energy_trend_score)

            return max(0, min(1, distribution_score))

        except Exception as e:
            print(f"能量分布计算失败: {e}")
            return 0.0

    def hho_optimize_ceemdan(self, max_iter: int = 25, n_hawks: int = 12) -> Dict:
        """
        使用哈里斯鹰优化算法优化CEEMDAN参数 (带进度条和可视化)

        Args:
            max_iter: 最大迭代次数
            n_hawks: 鹰群数量

        Returns:
            优化结果
        """
        if not HAS_PYEMD:
            raise ImportError("请安装PyEMD: pip install EMD-signal")

        if self.original_data is None:
            raise ValueError("请先加载数据")

        # 导入进度条库
        try:
            from tqdm import tqdm
            HAS_TQDM = True
        except ImportError:
            HAS_TQDM = False

        # 导入增强进度监控器
        try:
            from enhanced_progress_monitor import HHOProgressTracker, ProgressMonitor
            HAS_ENHANCED_PROGRESS = True
        except ImportError:
            HAS_ENHANCED_PROGRESS = False

        # 导入HHO优化器
        try:
            from harris_hawks_optimizer import HarrisHawksOptimizer
        except ImportError:
            raise ImportError("请确保harris_hawks_optimizer.py文件存在")

        import time

        print("🦅 开始HHO-CEEMDAN参数优化...")
        print(f"📊 鹰群数量: {n_hawks}, 最大迭代次数: {max_iter}")
        print("🎯 优化目标: 最小化重构误差 + 最大化正交性 + 最小化模态混叠 + PE增强")
        print("=" * 70)

        # 参数边界 [trials, noise_std, max_imf]
        lb = np.array([20, 0.002, 6])    # 下界
        ub = np.array([150, 0.015, 10])  # 上界

        print("🔧 参数搜索范围:")
        print(f"   trials: [{lb[0]:.0f}, {ub[0]:.0f}]")
        print(f"   noise_std: [{lb[1]:.3f}, {ub[1]:.3f}]")
        print(f"   max_imf: [{lb[2]:.0f}, {ub[2]:.0f}]")
        print()

        # 创建HHO优化器
        print("🚀 初始化哈里斯鹰群...")
        hho = HarrisHawksOptimizer(n_hawks=n_hawks, max_iter=max_iter, bounds=(lb, ub))

        # 定义适应度函数
        def fitness_func(params):
            return self.fitness_function(params, self.original_data)

        # 执行HHO优化
        print("📈 开始HHO优化...")

        def progress_callback(iteration, fitness):
            if iteration % 5 == 0 or iteration == max_iter:
                print(f"   第{iteration:2d}/{max_iter}代: 最佳适应度 = {fitness:.6f}")

        start_time = time.time()
        result = hho.optimize(fitness_func, progress_callback)
        optimization_time = time.time() - start_time

        # 处理HHO优化结果
        best_params = result['best_position']
        best_fitness = result['best_fitness']
        convergence_curve = result['convergence_curve']

        print(f"\n🏆 HHO优化完成!")
        print(f"⏱️  优化用时: {optimization_time:.2f} 秒")
        print(f"🎯 最佳适应度: {best_fitness:.6f}")
        print(f"🦅 最优参数: trials={int(best_params[0])}, noise_std={best_params[1]:.6f}, max_imf={int(best_params[2])}")
        print("=" * 70)

        # 保存HHO优化结果
        self.hho_results = {
            'best_params': {
                'trials': int(best_params[0]),
                'noise_std': best_params[1],
                'max_imf': int(best_params[2])
            },
            'best_fitness': best_fitness,
            'convergence_curve': convergence_curve,
            'optimization_time': optimization_time,
            'n_evaluations': result['n_evaluations']
        }

        return self.hho_results



    def ceemdan_decomposition(self, use_optimal_params: bool = True,
                             custom_params: Optional[Dict] = None) -> Dict:
        """
        使用优化参数进行CEEMDAN分解

        Args:
            use_optimal_params: 是否使用HHO优化的参数
            custom_params: 自定义参数字典

        Returns:
            分解结果
        """
        if not HAS_PYEMD:
            raise ImportError("请安装PyEMD: pip install EMD-signal")

        if self.original_data is None:
            raise ValueError("请先加载数据")

        # 确定使用的参数
        if use_optimal_params and self.optimal_params is not None:
            params = self.optimal_params
            print("使用HHO优化参数进行CEEMDAN分解...")
        elif custom_params is not None:
            params = custom_params
            print("使用自定义参数进行CEEMDAN分解...")
        else:
            # 使用默认参数
            params = {'trials': 100, 'noise_std': 0.005, 'max_imf': 8}
            print("使用默认参数进行CEEMDAN分解...")

        print(f"分解参数: {params}")

        # 执行CEEMDAN分解
        ceemdan = CEEMDAN(trials=params['trials'], noise_width=params['noise_std'])
        imfs = ceemdan.ceemdan(self.original_data, max_imf=params['max_imf'])

        print(f"CEEMDAN分解完成，获得 {len(imfs)} 个分量")

        # 计算每个IMF的频率特性
        imf_frequencies = []
        imf_energies = []

        for i, imf in enumerate(imfs):
            # 计算主导频率
            fft_imf = fft(imf)
            freqs = fftfreq(len(imf), 1/self.sampling_freq)
            power_spec = np.abs(fft_imf)**2

            # 找到最大功率对应的频率
            max_power_idx = np.argmax(power_spec[1:len(power_spec)//2]) + 1
            dominant_freq = abs(freqs[max_power_idx])
            imf_frequencies.append(dominant_freq)

            # 计算能量
            energy = np.sum(imf**2)
            imf_energies.append(energy)

            print(f"IMF{i+1}: 主导频率 = {dominant_freq:.6f} Hz, 能量 = {energy:.2f}")

        # 基于频率分类高频和低频分量
        cutoff_freq = 1.0 / (6 * 3600) * self.sampling_freq  # 6小时对应的频率
        print(f"高低频分割频率: {cutoff_freq:.6f} Hz")

        high_freq_imfs = []
        low_freq_imfs = []

        for i, freq in enumerate(imf_frequencies[:-1]):  # 排除趋势项
            if freq > cutoff_freq:
                high_freq_imfs.append(i)
            else:
                low_freq_imfs.append(i)

        print(f"高频IMF索引: {high_freq_imfs}")
        print(f"低频IMF索引: {low_freq_imfs}")

        # 重构高频和低频信号
        high_freq_signal = np.sum([imfs[i] for i in high_freq_imfs], axis=0) if high_freq_imfs else np.zeros_like(self.original_data)
        low_freq_signal = np.sum([imfs[i] for i in low_freq_imfs], axis=0) + imfs[-1]  # 包含趋势

        # 计算重构误差
        reconstruction_error = np.mean(np.abs(
            self.original_data - (high_freq_signal + low_freq_signal)
        ))

        result = {
            'method': 'CEEMDAN',
            'params': params,
            'imfs': imfs,
            'imf_frequencies': imf_frequencies,
            'imf_energies': imf_energies,
            'high_freq_indices': high_freq_imfs,
            'low_freq_indices': low_freq_imfs,
            'high_freq_signal': high_freq_signal,
            'low_freq_signal': low_freq_signal,
            'residue': imfs[-1],
            'reconstruction_error': reconstruction_error,
            'cutoff_frequency': cutoff_freq
        }

        self.decomposition_results['CEEMDAN'] = result
        print(f"CEEMDAN分解完成，重构误差: {reconstruction_error:.6f}")

        return result

    def calculate_imf_comprehensive_features(self, imf: np.ndarray,
                                           instantaneous_amplitude: np.ndarray,
                                           instantaneous_frequency: np.ndarray) -> Dict:
        """
        计算IMF的综合特征 (支持分而治之策略)

        Args:
            imf: IMF分量
            instantaneous_amplitude: 瞬时幅度
            instantaneous_frequency: 瞬时频率

        Returns:
            综合特征字典
        """
        try:
            # 1. 变异性特征 (高频信号应该变异性更强)
            variability = np.std(imf) / (np.mean(np.abs(imf)) + 1e-8)

            # 2. 周期性特征 (低频信号应该周期性更强)
            autocorr = np.correlate(imf, imf, mode='full')
            autocorr = autocorr[autocorr.size // 2:]
            autocorr = autocorr / autocorr[0]
            periodicity = np.max(autocorr[1:min(len(autocorr)//4, 100)]) if len(autocorr) > 1 else 0

            # 3. 趋势强度 (低频信号应该趋势性更强)
            x = np.arange(len(imf))
            slope, intercept, r_value, _, _ = stats.linregress(x, imf)
            trend_strength = r_value ** 2

            # 4. 频率稳定性 (低频信号频率应该更稳定)
            valid_freq = instantaneous_frequency[instantaneous_frequency > 0]
            freq_stability = 1.0 / (np.std(valid_freq) + 1e-8) if len(valid_freq) > 0 else 0

            # 5. 能量集中度
            energy = np.sum(imf**2)

            # 6. 幅度变化率 (高频信号幅度变化应该更剧烈)
            amplitude_change_rate = np.mean(np.abs(np.diff(instantaneous_amplitude)))

            # 7. 频率变化率 (高频信号频率变化应该更剧烈)
            freq_change_rate = np.mean(np.abs(np.diff(valid_freq))) if len(valid_freq) > 1 else 0

            # 8. 排列熵复杂度 (新增 - PE增强)
            complexity_pe = 0.0
            complexity_wpe = 0.0
            if HAS_PE_MODULE:
                try:
                    from config import DECOMPOSITION_CONFIG
                    pe_config = DECOMPOSITION_CONFIG.get('permutation_entropy_config', {
                        'order': 3, 'delay': 1, 'normalize': True, 'enable_weighted_pe': True
                    })

                    complexity_pe = permutation_entropy(imf, pe_config['order'],
                                                       pe_config['delay'], pe_config['normalize'])

                    if pe_config['enable_weighted_pe']:
                        complexity_wpe = weighted_permutation_entropy(imf, pe_config['order'],
                                                                     pe_config['delay'], pe_config['normalize'])
                    else:
                        complexity_wpe = complexity_pe

                except Exception as e:
                    print(f"⚠️  IMF排列熵计算失败: {e}")

            return {
                'variability': variability,
                'periodicity': periodicity,
                'trend_strength': trend_strength,
                'freq_stability': freq_stability,
                'energy': energy,
                'amplitude_change_rate': amplitude_change_rate,
                'freq_change_rate': freq_change_rate,
                'complexity_pe': complexity_pe,           # 标准排列熵
                'complexity_wpe': complexity_wpe,         # 加权排列熵
                'complexity_score': 0.6 * complexity_pe + 0.4 * complexity_wpe  # 综合复杂度
            }

        except Exception as e:
            print(f"特征计算失败: {e}")
            return {
                'variability': 0, 'periodicity': 0, 'trend_strength': 0,
                'freq_stability': 0, 'energy': 0, 'amplitude_change_rate': 0,
                'freq_change_rate': 0
            }

    def find_optimal_separation_point(self, imf_features: List[Dict]) -> int:
        """
        寻找最优分离点 - 特征最大化分离

        Args:
            imf_features: IMF特征列表

        Returns:
            最优分离点索引
        """
        if len(imf_features) < 2:
            return 1

        best_separation = 1
        max_feature_contrast = 0

        # 尝试不同的分离点
        for sep_point in range(1, len(imf_features)):
            # 计算高频组和低频组的特征对比度
            high_freq_group = imf_features[:sep_point]
            low_freq_group = imf_features[sep_point:]

            if len(high_freq_group) == 0 or len(low_freq_group) == 0:
                continue

            # 计算组内特征均值 (包含排列熵)
            high_features = {
                'variability': np.mean([f['variability'] for f in high_freq_group]),
                'periodicity': np.mean([f['periodicity'] for f in high_freq_group]),
                'trend_strength': np.mean([f['trend_strength'] for f in high_freq_group]),
                'freq_stability': np.mean([f['freq_stability'] for f in high_freq_group]),
                'complexity_score': np.mean([f.get('complexity_score', 0) for f in high_freq_group])
            }

            low_features = {
                'variability': np.mean([f['variability'] for f in low_freq_group]),
                'periodicity': np.mean([f['periodicity'] for f in low_freq_group]),
                'trend_strength': np.mean([f['trend_strength'] for f in low_freq_group]),
                'freq_stability': np.mean([f['freq_stability'] for f in low_freq_group]),
                'complexity_score': np.mean([f.get('complexity_score', 0) for f in low_freq_group])
            }

            # 计算特征对比度 (期望高频变异性强，低频周期性和趋势性强，高频复杂度高)
            contrast_score = (
                (high_features['variability'] - low_features['variability']) +  # 高频变异性更强
                (low_features['periodicity'] - high_features['periodicity']) +  # 低频周期性更强
                (low_features['trend_strength'] - high_features['trend_strength']) +  # 低频趋势性更强
                (low_features['freq_stability'] - high_features['freq_stability']) +  # 低频频率更稳定
                (high_features['complexity_score'] - low_features['complexity_score'])  # 高频复杂度更高 (PE增强)
            )

            if contrast_score > max_feature_contrast:
                max_feature_contrast = contrast_score
                best_separation = sep_point

        return best_separation

    def evaluate_separation_quality(self, high_freq_signal: np.ndarray,
                                   low_freq_signal: np.ndarray,
                                   imf_features: List[Dict],
                                   separation_point: int) -> Dict:
        """
        评估分离质量

        Args:
            high_freq_signal: 高频信号
            low_freq_signal: 低频信号
            imf_features: IMF特征
            separation_point: 分离点

        Returns:
            分离质量评估结果
        """
        try:
            # 1. 重构质量
            reconstructed = high_freq_signal + low_freq_signal
            reconstruction_error = np.mean((self.original_data - reconstructed)**2)
            reconstruction_quality = 1.0 / (1.0 + reconstruction_error)

            # 2. 特征区分度
            if separation_point > 0 and separation_point < len(imf_features):
                high_group = imf_features[:separation_point]
                low_group = imf_features[separation_point:]

                high_variability = np.mean([f['variability'] for f in high_group])
                low_variability = np.mean([f['variability'] for f in low_group])

                high_periodicity = np.mean([f['periodicity'] for f in high_group])
                low_periodicity = np.mean([f['periodicity'] for f in low_group])

                feature_distinction = abs(high_variability - low_variability) + abs(high_periodicity - low_periodicity)
            else:
                feature_distinction = 0

            # 3. 信号特征评估
            high_freq_features = self.calculate_imf_comprehensive_features(
                high_freq_signal, np.abs(hilbert(high_freq_signal)),
                np.diff(np.unwrap(np.angle(hilbert(high_freq_signal)))) / (2.0 * np.pi) * self.sampling_freq
            )

            low_freq_features = self.calculate_imf_comprehensive_features(
                low_freq_signal, np.abs(hilbert(low_freq_signal)),
                np.diff(np.unwrap(np.angle(hilbert(low_freq_signal)))) / (2.0 * np.pi) * self.sampling_freq
            )

            # 4. 综合质量得分
            overall_score = (0.4 * reconstruction_quality +
                           0.3 * min(1.0, feature_distinction) +
                           0.2 * (high_freq_features['variability'] / (low_freq_features['variability'] + 1e-8)) +
                           0.1 * (low_freq_features['periodicity'] / (high_freq_features['periodicity'] + 1e-8)))

            return {
                'reconstruction_quality': reconstruction_quality,
                'feature_distinction': feature_distinction,
                'high_freq_features': high_freq_features,
                'low_freq_features': low_freq_features,
                'overall_score': min(1.0, overall_score)
            }

        except Exception as e:
            print(f"分离质量评估失败: {e}")
            return {
                'reconstruction_quality': 0,
                'feature_distinction': 0,
                'high_freq_features': {},
                'low_freq_features': {},
                'overall_score': 0
            }

    def hilbert_analysis(self, use_ceemdan_result: bool = True,
                        imfs: Optional[List[np.ndarray]] = None) -> Dict:
        """
        对IMF分量进行Hilbert变换分析

        Args:
            use_ceemdan_result: 是否使用CEEMDAN分解结果
            imfs: 自定义IMF分量列表

        Returns:
            Hilbert分析结果
        """
        if use_ceemdan_result:
            if 'CEEMDAN' not in self.decomposition_results:
                raise ValueError("请先进行CEEMDAN分解")
            imfs = self.decomposition_results['CEEMDAN']['imfs']
        elif imfs is None:
            raise ValueError("请提供IMF分量或先进行CEEMDAN分解")

        print("🚀 开始增强版Hilbert变换分析...")
        print("🎯 目标: 特征最大化分离，支持分而治之策略")

        # 1. 计算每个IMF的多维特征
        imf_features = []
        hilbert_results = []

        for i, imf in enumerate(imfs[:-1]):  # 排除残差项
            # Hilbert变换
            analytic_signal = hilbert(imf)
            instantaneous_amplitude = np.abs(analytic_signal)
            instantaneous_phase = np.unwrap(np.angle(analytic_signal))
            instantaneous_frequency = np.diff(instantaneous_phase) / (2.0 * np.pi) * self.sampling_freq

            # 处理边界效应
            instantaneous_frequency = np.concatenate([[instantaneous_frequency[0]], instantaneous_frequency])

            # 基础频率特征
            valid_freq = instantaneous_frequency[instantaneous_frequency > 0]
            mean_freq = np.mean(valid_freq) if len(valid_freq) > 0 else 0
            std_freq = np.std(valid_freq) if len(valid_freq) > 0 else 0
            median_freq = np.median(valid_freq) if len(valid_freq) > 0 else 0

            # 计算综合特征
            comprehensive_features = self.calculate_imf_comprehensive_features(
                imf, instantaneous_amplitude, instantaneous_frequency
            )
            comprehensive_features.update({
                'imf_index': i,
                'mean_frequency': mean_freq,
                'std_frequency': std_freq,
                'median_frequency': median_freq,
                'instantaneous_amplitude': instantaneous_amplitude,
                'instantaneous_phase': instantaneous_phase,
                'instantaneous_frequency': instantaneous_frequency
            })

            imf_features.append(comprehensive_features)
            hilbert_results.append({
                'imf_index': i,
                'instantaneous_amplitude': instantaneous_amplitude,
                'instantaneous_phase': instantaneous_phase,
                'instantaneous_frequency': instantaneous_frequency,
                'mean_frequency': mean_freq,
                'std_frequency': std_freq,
                'median_frequency': median_freq
            })

            print(f"📊 IMF{i+1}: 频率={mean_freq:.6f}Hz, 变异性={comprehensive_features['variability']:.4f}, "
                  f"周期性={comprehensive_features['periodicity']:.4f}")

        # 2. 智能分界点决策
        optimal_separation = self.find_optimal_separation_point(imf_features)

        # 3. 基于最优分离点分组
        hilbert_high_freq_imfs = list(range(optimal_separation))
        hilbert_low_freq_imfs = list(range(optimal_separation, len(imfs)-1))

        print(f"🎯 最优分离点: 第{optimal_separation}个IMF")
        print(f"📊 高频IMF索引: {hilbert_high_freq_imfs}")
        print(f"📊 低频IMF索引: {hilbert_low_freq_imfs}")

        # 4. 重构信号
        hilbert_high_freq_signal = np.sum([imfs[i] for i in hilbert_high_freq_imfs], axis=0) if hilbert_high_freq_imfs else np.zeros_like(self.original_data)
        hilbert_low_freq_signal = np.sum([imfs[i] for i in hilbert_low_freq_imfs], axis=0) + imfs[-1]

        # 5. 分离质量评估
        separation_quality = self.evaluate_separation_quality(
            hilbert_high_freq_signal, hilbert_low_freq_signal, imf_features, optimal_separation
        )

        # 6. 计算重构误差
        hilbert_reconstruction_error = np.mean(np.abs(
            self.original_data - (hilbert_high_freq_signal + hilbert_low_freq_signal)
        ))

        enhanced_hilbert_result = {
            'method': 'Enhanced-Hilbert-MultiFeature',
            'hilbert_results': hilbert_results,
            'imf_features': imf_features,
            'optimal_separation_point': optimal_separation,
            'high_freq_indices': hilbert_high_freq_imfs,
            'low_freq_indices': hilbert_low_freq_imfs,
            'high_freq_signal': hilbert_high_freq_signal,
            'low_freq_signal': hilbert_low_freq_signal,
            'separation_quality': separation_quality,
            'reconstruction_error': hilbert_reconstruction_error
        }

        self.decomposition_results['Hilbert'] = enhanced_hilbert_result

        print(f"✅ 增强Hilbert分析完成")
        print(f"🎯 分离质量得分: {separation_quality['overall_score']:.4f}")
        print(f"🔧 重构误差: {hilbert_reconstruction_error:.6f}")
        print(f"📈 高频信号变异性: {separation_quality['high_freq_features'].get('variability', 0):.4f}")
        print(f"📉 低频信号周期性: {separation_quality['low_freq_features'].get('periodicity', 0):.4f}")

        return enhanced_hilbert_result

    def complete_decomposition(self, optimize_params: bool = True,
                              max_iter: int = 25, n_hawks: int = 12) -> Dict:
        """
        完整的HHO-CEEMDAN-Hilbert分解流程

        Args:
            optimize_params: 是否进行参数优化
            max_iter: HHO最大迭代次数
            n_hawks: HHO鹰群数量

        Returns:
            完整分解结果
        """
        if self.original_data is None:
            raise ValueError("请先加载数据")

        print("=" * 60)
        print("开始完整的HHO-CEEMDAN-Hilbert分解流程")
        print("=" * 60)

        # 步骤1: 频率谱分析
        print("\n步骤1: 频率谱分析")
        self.analyze_frequency_spectrum()

        # 步骤2: HHO参数优化（可选）
        if optimize_params:
            print("\n步骤2: HHO参数优化")
            self.hho_optimize_ceemdan(max_iter=max_iter, n_hawks=n_hawks)
        else:
            print("\n步骤2: 跳过参数优化，使用默认参数")

        # 步骤3: CEEMDAN分解
        print("\n步骤3: CEEMDAN分解")
        ceemdan_result = self.ceemdan_decomposition(use_optimal_params=optimize_params)

        # 步骤4: Hilbert变换分析
        print("\n步骤4: Hilbert变换分析")
        hilbert_result = self.hilbert_analysis()

        # 步骤5: 结果对比和选择最佳分解
        print("\n步骤5: 结果对比")
        ceemdan_error = ceemdan_result['reconstruction_error']
        hilbert_error = hilbert_result['reconstruction_error']

        print(f"CEEMDAN重构误差: {ceemdan_error:.6f}")
        print(f"Hilbert优化重构误差: {hilbert_error:.6f}")

        if hilbert_error < ceemdan_error:
            print("选择Hilbert优化结果作为最终分解")
            final_result = {
                'method': 'HHO-CEEMDAN-Hilbert',
                'high_freq_signal': hilbert_result['high_freq_signal'],
                'low_freq_signal': hilbert_result['low_freq_signal'],
                'reconstruction_error': hilbert_error,
                'high_freq_indices': hilbert_result['high_freq_indices'],
                'low_freq_indices': hilbert_result['low_freq_indices'],
                'used_hilbert_optimization': True
            }
        else:
            print("选择CEEMDAN结果作为最终分解")
            final_result = {
                'method': 'HHO-CEEMDAN',
                'high_freq_signal': ceemdan_result['high_freq_signal'],
                'low_freq_signal': ceemdan_result['low_freq_signal'],
                'reconstruction_error': ceemdan_error,
                'high_freq_indices': ceemdan_result['high_freq_indices'],
                'low_freq_indices': ceemdan_result['low_freq_indices'],
                'used_hilbert_optimization': False
            }

        # 添加通用信息
        final_result.update({
            'original_data': self.original_data,
            'sampling_freq': self.sampling_freq,
            'cutoff_frequency': ceemdan_result['cutoff_frequency'],
            'imfs': ceemdan_result['imfs'],
            'optimization_used': optimize_params,
            'hho_results': self.hho_results if optimize_params else None
        })

        self.decomposition_results['Final'] = final_result

        print("\n" + "=" * 60)
        print("完整分解流程完成!")
        print(f"最终方法: {final_result['method']}")
        print(f"最终重构误差: {final_result['reconstruction_error']:.6f}")
        print(f"高频分量数: {len(final_result['high_freq_indices'])}")
        print(f"低频分量数: {len(final_result['low_freq_indices'])}")
        print("=" * 60)

        return final_result

    def save_decomposition_results(self, save_path: str = None) -> str:
        """
        保存分解结果

        Args:
            save_path: 保存路径，如果为None则使用默认路径

        Returns:
            保存文件路径
        """
        if not self.decomposition_results:
            raise ValueError("没有分解结果可保存")

        if save_path is None:
            save_path = os.path.join(os.path.dirname(__file__), 'decomposition_results.pkl')

        # 准备保存的数据
        save_data = {
            'decomposition_results': self.decomposition_results,
            'frequency_analysis': self.frequency_analysis,
            'hho_results': self.hho_results,
            'sampling_freq': self.sampling_freq,
            'optimal_params': self.optimal_params
        }

        joblib.dump(save_data, save_path)
        print(f"分解结果已保存到: {save_path}")

        return save_path

    def load_decomposition_results(self, load_path: str) -> Dict:
        """
        加载分解结果

        Args:
            load_path: 加载路径

        Returns:
            加载的分解结果
        """
        if not os.path.exists(load_path):
            raise FileNotFoundError(f"文件不存在: {load_path}")

        save_data = joblib.load(load_path)

        self.decomposition_results = save_data['decomposition_results']
        self.frequency_analysis = save_data['frequency_analysis']
        self.hho_results = save_data['hho_results']
        self.sampling_freq = save_data['sampling_freq']
        self.optimal_params = save_data['optimal_params']

        print(f"分解结果已从 {load_path} 加载")

        return self.decomposition_results
