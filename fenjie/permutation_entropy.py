"""
排列熵（Permutation Entropy）计算模块
用于增强GWO-CEEMDAN-Hilbert分解中的高低频分离质量

排列熵是一种量化时间序列复杂度和不规律性的非线性动力学指标，
特别适用于风电功率信号的复杂度分析。

作者: AI Assistant
版本: 1.0
日期: 2024
"""

import numpy as np
from typing import List, Tuple, Dict, Optional
import warnings
warnings.filterwarnings('ignore')

def ordinal_patterns(x: np.ndarray, order: int = 3, delay: int = 1) -> np.ndarray:
    """
    计算时间序列的序数模式
    
    Args:
        x: 输入时间序列
        order: 嵌入维数 (推荐3-7)
        delay: 时间延迟 (通常为1)
        
    Returns:
        序数模式数组
    """
    N = len(x)
    patterns = []
    
    for i in range(N - delay * (order - 1)):
        # 提取嵌入向量
        embedded_vector = []
        for j in range(order):
            embedded_vector.append(x[i + j * delay])
        
        # 计算排列模式
        sorted_indices = np.argsort(embedded_vector)
        patterns.append(tuple(sorted_indices.tolist()))
    
    return patterns

def permutation_entropy(x: np.ndarray, order: int = 3, delay: int = 1, 
                       normalize: bool = True) -> float:
    """
    计算排列熵
    
    Args:
        x: 输入时间序列
        order: 嵌入维数
        delay: 时间延迟
        normalize: 是否归一化到[0,1]
        
    Returns:
        排列熵值
    """
    # 检查输入
    if len(x) < order:
        return 0.0
    
    # 计算序数模式
    patterns = ordinal_patterns(x, order, delay)
    
    if len(patterns) == 0:
        return 0.0
    
    # 统计模式频率
    from collections import Counter
    pattern_counts = Counter(patterns)
    unique_patterns = list(pattern_counts.keys())
    counts = np.array(list(pattern_counts.values()))
    
    # 计算相对频率
    probabilities = counts / len(patterns)
    
    # 计算排列熵
    pe = -np.sum(probabilities * np.log2(probabilities + 1e-12))
    
    # 归一化
    if normalize:
        max_entropy = np.log2(np.math.factorial(order))
        pe = pe / max_entropy if max_entropy > 0 else 0
    
    return pe

def weighted_permutation_entropy(x: np.ndarray, order: int = 3, delay: int = 1,
                                normalize: bool = True) -> float:
    """
    计算加权排列熵 (考虑相对方差)
    
    Args:
        x: 输入时间序列
        order: 嵌入维数
        delay: 时间延迟
        normalize: 是否归一化
        
    Returns:
        加权排列熵值
    """
    if len(x) < order:
        return 0.0
    
    N = len(x)
    patterns = []
    weights = []
    
    for i in range(N - delay * (order - 1)):
        # 提取嵌入向量
        embedded_vector = []
        for j in range(order):
            embedded_vector.append(x[i + j * delay])
        
        # 计算排列模式
        sorted_indices = np.argsort(embedded_vector)
        patterns.append(tuple(sorted_indices.tolist()))
        
        # 计算相对方差作为权重
        relative_variance = np.var(embedded_vector) / (np.mean(np.abs(embedded_vector)) + 1e-12)
        weights.append(relative_variance)
    
    if len(patterns) == 0:
        return 0.0

    # 统计加权模式频率
    from collections import defaultdict
    pattern_weights = defaultdict(float)

    for i, pattern in enumerate(patterns):
        pattern_weights[pattern] += weights[i]

    weighted_probabilities = list(pattern_weights.values())
    
    # 归一化概率
    total_weight = np.sum(weighted_probabilities)
    if total_weight > 0:
        weighted_probabilities = np.array(weighted_probabilities) / total_weight
    else:
        return 0.0
    
    # 计算加权排列熵
    wpe = -np.sum(weighted_probabilities * np.log2(weighted_probabilities + 1e-12))
    
    # 归一化
    if normalize:
        max_entropy = np.log2(np.math.factorial(order))
        wpe = wpe / max_entropy if max_entropy > 0 else 0
    
    return wpe

def multiscale_permutation_entropy(x: np.ndarray, scales: List[int] = None,
                                  order: int = 3, delay: int = 1) -> Dict[int, float]:
    """
    计算多尺度排列熵
    
    Args:
        x: 输入时间序列
        scales: 尺度列表
        order: 嵌入维数
        delay: 时间延迟
        
    Returns:
        各尺度的排列熵字典
    """
    if scales is None:
        scales = [1, 2, 3, 4, 5]
    
    mpe_values = {}
    
    for scale in scales:
        if scale == 1:
            coarse_grained = x
        else:
            # 粗粒化处理
            N = len(x)
            coarse_length = N // scale
            coarse_grained = []
            
            for i in range(coarse_length):
                start_idx = i * scale
                end_idx = start_idx + scale
                coarse_grained.append(np.mean(x[start_idx:end_idx]))
            
            coarse_grained = np.array(coarse_grained)
        
        # 计算该尺度的排列熵
        if len(coarse_grained) >= order:
            pe_value = permutation_entropy(coarse_grained, order, delay)
            mpe_values[scale] = pe_value
        else:
            mpe_values[scale] = 0.0
    
    return mpe_values

def calculate_pe_based_complexity_score(imfs: List[np.ndarray], 
                                       config: Dict = None) -> Dict:
    """
    基于排列熵计算IMF复杂度得分
    
    Args:
        imfs: IMF分量列表
        config: 排列熵配置
        
    Returns:
        复杂度分析结果
    """
    if config is None:
        config = {
            'order': 3,
            'delay': 1,
            'normalize': True,
            'enable_weighted_pe': True,
            'complexity_threshold': 0.5
        }
    
    results = {
        'pe_values': [],
        'wpe_values': [],
        'complexity_scores': [],
        'high_complexity_indices': [],
        'low_complexity_indices': [],
        'separation_quality': 0.0
    }
    
    # 计算每个IMF的排列熵
    for i, imf in enumerate(imfs[:-1]):  # 排除残差项
        # 标准排列熵
        pe = permutation_entropy(imf, config['order'], config['delay'], config['normalize'])
        results['pe_values'].append(pe)
        
        # 加权排列熵
        if config['enable_weighted_pe']:
            wpe = weighted_permutation_entropy(imf, config['order'], config['delay'], config['normalize'])
            results['wpe_values'].append(wpe)
        else:
            results['wpe_values'].append(pe)
        
        # 综合复杂度得分 (结合PE和WPE)
        complexity_score = 0.6 * pe + 0.4 * results['wpe_values'][-1]
        results['complexity_scores'].append(complexity_score)
    
    # 基于复杂度阈值分类
    threshold = config['complexity_threshold']
    
    for i, score in enumerate(results['complexity_scores']):
        if score > threshold:
            results['high_complexity_indices'].append(i)
        else:
            results['low_complexity_indices'].append(i)
    
    # 计算分离质量
    if len(results['high_complexity_indices']) > 0 and len(results['low_complexity_indices']) > 0:
        high_scores = [results['complexity_scores'][i] for i in results['high_complexity_indices']]
        low_scores = [results['complexity_scores'][i] for i in results['low_complexity_indices']]
        
        high_mean = np.mean(high_scores)
        low_mean = np.mean(low_scores)
        
        # 分离质量 = 高低复杂度组间差异 / 组内方差
        separation_quality = abs(high_mean - low_mean) / (np.std(high_scores + low_scores) + 1e-8)
        results['separation_quality'] = min(1.0, separation_quality)
    
    return results

def pe_enhanced_fitness_component(imfs: List[np.ndarray], 
                                 cutoff_freq: float,
                                 sampling_freq: float,
                                 config: Dict = None) -> float:
    """
    基于排列熵的适应度函数组件
    
    Args:
        imfs: IMF分量列表
        cutoff_freq: 高低频分割频率
        sampling_freq: 采样频率
        config: 排列熵配置
        
    Returns:
        排列熵适应度得分 (0-1, 越大越好)
    """
    if config is None:
        config = {
            'order': 3,
            'delay': 1,
            'normalize': True,
            'enable_weighted_pe': True,
            'complexity_threshold': 0.5
        }
    
    # 计算复杂度分析
    pe_results = calculate_pe_based_complexity_score(imfs, config)
    
    # 计算频率分类 (传统方法)
    freq_high_indices = []
    freq_low_indices = []
    
    for i, imf in enumerate(imfs[:-1]):
        # 计算主导频率
        fft_imf = np.fft.fft(imf)
        freqs = np.fft.fftfreq(len(imf), 1/sampling_freq)
        power_spec = np.abs(fft_imf)**2
        
        max_power_idx = np.argmax(power_spec[1:len(power_spec)//2]) + 1
        dominant_freq = abs(freqs[max_power_idx])
        
        if dominant_freq > cutoff_freq:
            freq_high_indices.append(i)
        else:
            freq_low_indices.append(i)
    
    # 计算PE分类与频率分类的一致性
    pe_high_set = set(pe_results['high_complexity_indices'])
    pe_low_set = set(pe_results['low_complexity_indices'])
    freq_high_set = set(freq_high_indices)
    freq_low_set = set(freq_low_indices)
    
    # 一致性得分
    consistency_score = 0.0
    total_imfs = len(imfs) - 1
    
    if total_imfs > 0:
        # 高频一致性
        high_consistency = len(pe_high_set & freq_high_set) / max(1, len(freq_high_set))
        # 低频一致性
        low_consistency = len(pe_low_set & freq_low_set) / max(1, len(freq_low_set))
        
        consistency_score = 0.5 * high_consistency + 0.5 * low_consistency
    
    # 综合得分 = 分离质量 × 一致性
    final_score = pe_results['separation_quality'] * consistency_score
    
    return final_score

if __name__ == "__main__":
    # 测试代码
    print("🧪 排列熵模块测试")
    
    # 生成测试信号
    t = np.linspace(0, 10, 1000)
    high_freq_signal = np.sin(2 * np.pi * 5 * t) + 0.1 * np.random.randn(1000)  # 高频+噪声
    low_freq_signal = np.sin(2 * np.pi * 0.5 * t)  # 低频
    
    # 计算排列熵
    pe_high = permutation_entropy(high_freq_signal)
    pe_low = permutation_entropy(low_freq_signal)
    
    print(f"高频信号PE: {pe_high:.4f}")
    print(f"低频信号PE: {pe_low:.4f}")
    print(f"复杂度差异: {pe_high - pe_low:.4f}")
    
    # 计算加权排列熵
    wpe_high = weighted_permutation_entropy(high_freq_signal)
    wpe_low = weighted_permutation_entropy(low_freq_signal)
    
    print(f"高频信号WPE: {wpe_high:.4f}")
    print(f"低频信号WPE: {wpe_low:.4f}")
    print(f"加权复杂度差异: {wpe_high - wpe_low:.4f}")
    
    print("✅ 排列熵模块测试完成")
