"""
带详细进度条的HHO-CEEMDAN-Hilbert功率分解脚本
为每个计算步骤提供实时进度显示
集成排列熵(PE)增强分析
"""

import os
import sys
import time
import numpy as np
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

# 设置环境变量
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 添加当前目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入进度条库
try:
    from tqdm import tqdm
    HAS_TQDM = True
except ImportError:
    HAS_TQDM = False
    print("⚠️  tqdm未安装，将使用简单进度显示")

class ProgressTracker:
    """统一的进度跟踪器"""
    
    def __init__(self, total, desc="Progress", unit="it"):
        self.total = total
        self.desc = desc
        self.unit = unit
        self.current = 0
        self.start_time = time.time()
        self.last_print_time = 0
        self.print_interval = 1.0  # 每秒更新一次
        
        if HAS_TQDM:
            self.pbar = tqdm(total=total, desc=desc, unit=unit)
        else:
            self.pbar = None
            print(f"🚀 开始 {desc} (总计: {total} {unit})")
    
    def update(self, n=1):
        """更新进度"""
        self.current += n
        current_time = time.time()
        
        if HAS_TQDM and self.pbar:
            self.pbar.update(n)
        else:
            # 控制打印频率
            if current_time - self.last_print_time >= self.print_interval or self.current >= self.total:
                self.print_progress()
                self.last_print_time = current_time
    
    def print_progress(self):
        """打印进度信息（无tqdm时使用）"""
        if HAS_TQDM:
            return
            
        elapsed = time.time() - self.start_time
        percentage = (self.current / self.total) * 100
        
        # 计算ETA
        if self.current > 0:
            eta = elapsed * (self.total - self.current) / self.current
        else:
            eta = 0
        
        # 创建进度条
        bar_length = 30
        filled_length = int(bar_length * self.current / self.total)
        bar = '█' * filled_length + '░' * (bar_length - filled_length)
        
        # 格式化时间
        def format_time(seconds):
            if seconds < 60:
                return f"{seconds:.0f}s"
            elif seconds < 3600:
                return f"{seconds//60:.0f}m{seconds%60:.0f}s"
            else:
                return f"{seconds//3600:.0f}h{(seconds%3600)//60:.0f}m"
        
        # 打印进度
        print(f"\r{self.desc}: {percentage:5.1f}% |{bar}| "
              f"{self.current}/{self.total} [{format_time(elapsed)}<{format_time(eta)}]", 
              end='', flush=True)
        
        if self.current >= self.total:
            print()  # 完成时换行
    
    def close(self):
        """关闭进度条"""
        if HAS_TQDM and self.pbar:
            self.pbar.close()
        else:
            if self.current < self.total:
                self.current = self.total
                self.print_progress()

class PowerDecompositionWithProgress:
    """带进度条的功率分解流水线"""
    
    def __init__(self, data_file: str = None):
        """初始化分解流水线"""
        # 导入配置
        from config import DECOMPOSITION_CONFIG
        self.config = DECOMPOSITION_CONFIG

        # 使用配置中的参数
        data_file = data_file or self.config['data_file']
        self.data_file = os.path.join(current_dir, data_file)
        self.power_column = self.config['power_column']
        self.sampling_freq = self.config['sampling_freq']
        self.use_full_data = self.config['use_full_data']
        self.sample_rate = self.config['sample_rate']

        # 创建结果目录
        self.results_dir = os.path.join(current_dir, 'results')
        os.makedirs(self.results_dir, exist_ok=True)

        self.results = {}
        self._stop_progress = False  # 用于控制进度显示线程
        self.original_data = None  # 初始化数据变量
        self.generated_files = {}  # 存储生成的文件名

        print("🎯 带进度条的功率分解流水线初始化完成")
        print(f"📁 数据文件: {data_file}")
        print(f"📁 结果目录: {self.results_dir}")
        print(f"⚡ 使用完整数据: {'是' if self.use_full_data else '否'}")
        print(f"📊 采样率: {self.sample_rate} (1=完整数据)")
    
    def load_data_with_progress(self, sample_rate=None):
        """带进度条的数据加载"""
        print("\n" + "=" * 60)
        print("步骤1: 数据加载和预处理")
        print("=" * 60)

        # 使用配置中的采样率
        if sample_rate is None:
            sample_rate = self.sample_rate

        # 数据加载进度
        progress = ProgressTracker(4, "数据加载", "步骤")

        try:
            # 步骤1: 读取文件
            progress.update(1)
            df = pd.read_csv(self.data_file)

            # 步骤2: 提取功率列
            progress.update(1)
            power_data = df[self.power_column].values

            # 步骤3: 数据采样（根据配置决定）
            progress.update(1)
            if self.use_full_data:
                sampled_data = power_data
                print(f"⚡ 使用完整数据集: {len(power_data)} 个数据点 (最高精度)")
            elif sample_rate > 1:
                sampled_data = power_data[::sample_rate]
                print(f"📉 数据采样: {len(power_data)} → {len(sampled_data)} (采样率: 1/{sample_rate})")
            else:
                sampled_data = power_data
            
            # 步骤4: 数据验证
            progress.update(1)
            self.original_data = sampled_data
            
            progress.close()
            
            print(f"✅ 数据加载完成")
            print(f"📊 数据长度: {len(self.original_data)}")
            print(f"📊 数据范围: [{np.min(self.original_data):.4f}, {np.max(self.original_data):.4f}]")
            
            return True
            
        except Exception as e:
            progress.close()
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def frequency_analysis_with_progress(self):
        """带进度条的频率分析"""
        print("\n" + "=" * 60)
        print("步骤2: 频率谱分析")
        print("=" * 60)
        
        progress = ProgressTracker(5, "频率分析", "步骤")
        
        try:
            # 步骤1: FFT计算
            progress.update(1)
            fft_result = np.fft.fft(self.original_data)
            
            # 步骤2: 频率轴计算
            progress.update(1)
            freqs = np.fft.fftfreq(len(self.original_data), 1/self.sampling_freq)
            
            # 步骤3: 功率谱密度
            progress.update(1)
            power_spectrum = np.abs(fft_result)**2
            
            # 步骤4: 主要频率成分识别
            progress.update(1)
            positive_freqs = freqs[:len(freqs)//2]
            positive_power = power_spectrum[:len(power_spectrum)//2]
            
            # 步骤5: 统计分析
            progress.update(1)
            sorted_indices = np.argsort(positive_power)[::-1]
            dominant_freqs = positive_freqs[sorted_indices]
            
            progress.close()
            
            self.frequency_analysis = {
                'frequencies': positive_freqs,
                'power_spectrum': positive_power,
                'dominant_frequencies': dominant_freqs[:10]
            }
            
            print(f"✅ 频率分析完成")
            print(f"📊 主要频率成分 (Hz): {dominant_freqs[:5]}")
            
            return True
            
        except Exception as e:
            progress.close()
            print(f"❌ 频率分析失败: {e}")
            return False
    
    def hho_optimization_with_progress(self, max_iter=15, n_hawks=8):
        """带详细进度条的HHO优化"""
        print("\n" + "=" * 60)
        print("步骤3: HHO参数优化")
        print("=" * 60)
        
        try:
            from PyEMD import CEEMDAN
        except ImportError:
            print("❌ PyEMD未安装")
            return False
        
        # 导入HHO优化器
        try:
            from harris_hawks_optimizer import HarrisHawksOptimizer
        except ImportError:
            print("❌ HHO优化器模块未找到")
            return False

        # 参数边界
        lb = np.array([10, 0.003, 5])    # trials, noise_std, max_imf
        ub = np.array([30, 0.010, 7])

        print(f"🦅 HHO配置: {max_iter}代 × {n_hawks}鹰")
        print(f"📊 参数范围: trials[{lb[0]}-{ub[0]}], noise_std[{lb[1]:.3f}-{ub[1]:.3f}], max_imf[{lb[2]}-{ub[2]}]")
        
        # 创建HHO优化器
        print("\n🚀 初始化哈里斯鹰群...")
        hho = HarrisHawksOptimizer(n_hawks=n_hawks, max_iter=max_iter, bounds=(lb, ub))

        # 计算初始适应度
        print("📈 计算初始适应度...")
        fitness_progress = ProgressTracker(n_hawks, "初始适应度", "鹰")

        # 初始化鹰群并计算适应度
        hawks = hho.initialize_population()
        fitness = []
        for i, hawk in enumerate(hawks):
            fit = self.fitness_function(hawk)
            fitness.append(fit)
            fitness_progress.update(1)

        fitness_progress.close()
        fitness = np.array(fitness)
        
        # 找到最佳鹰
        best_idx = np.argmin(fitness)
        best_hawk = hawks[best_idx].copy()
        best_fitness = fitness[best_idx]

        # 显示初始最优结果
        sorted_indices = np.argsort(fitness)
        print(f"🥇 初始最佳鹰适应度: {fitness[sorted_indices[0]]:.6f}")
        print(f"🥈 初始第二鹰适应度: {fitness[sorted_indices[1]]:.6f}")
        print(f"🥉 初始第三鹰适应度: {fitness[sorted_indices[2]]:.6f}")
        
        # HHO主循环
        print(f"\n🔄 开始HHO迭代优化...")
        convergence_curve = [best_fitness]

        # 创建迭代进度条
        iteration_progress = ProgressTracker(max_iter, "HHO迭代", "代")
        
        for iteration in range(max_iter):
            iter_start_time = time.time()

            # HHO位置更新
            E = 2 * np.random.random() - 1  # 逃逸能量

            if abs(E) >= 1:
                # 探索阶段
                new_hawks = hho.exploration_phase(hawks, iteration)
            else:
                # 开发阶段
                hho.best_hawk = best_hawk
                new_hawks = hho.exploitation_phase(hawks, iteration)

            # 适应度评估阶段（带进度条）
            eval_progress = ProgressTracker(n_hawks, f"第{iteration+1}代评估", "鹰")
            new_fitness = []
            for i, hawk in enumerate(new_hawks):
                fit = self.fitness_function(hawk)
                new_fitness.append(fit)
                eval_progress.update(1)
            eval_progress.close()
            new_fitness = np.array(new_fitness)

            # 贪婪选择和更新
            improved = False
            for i in range(n_hawks):
                if new_fitness[i] < fitness[i]:
                    hawks[i] = new_hawks[i]
                    fitness[i] = new_fitness[i]

                    # 更新全局最优
                    if fitness[i] < best_fitness:
                        best_fitness = fitness[i]
                        best_hawk = hawks[i].copy()
                        improved = True

            convergence_curve.append(best_fitness)

            # 计算迭代时间和ETA
            iter_time = time.time() - iter_start_time

            # 更新迭代进度条
            iteration_progress.update(1)

            # 详细进度报告
            improvement_str = "🔥" if improved else "  "
            print(f"   第{iteration+1:2d}/{max_iter}代 | 最佳: {best_fitness:.6f} {improvement_str} | 用时: {iter_time:.1f}s")

            # 每5代显示详细信息
            if (iteration + 1) % 5 == 0:
                print(f"   🏆 当前最优参数: trials={int(best_hawk[0])}, "
                      f"noise_std={best_hawk[1]:.4f}, max_imf={int(best_hawk[2])}")
        
        iteration_progress.close()

        # 保存HHO结果
        self.hho_results = {
            'best_params': {
                'trials': int(best_hawk[0]),
                'noise_std': best_hawk[1],
                'max_imf': int(best_hawk[2])
            },
            'best_fitness': best_fitness,
            'convergence_curve': convergence_curve
        }

        print(f"\n✅ HHO优化完成!")
        print(f"🏆 最优参数: {self.hho_results['best_params']}")
        print(f"📈 最优适应度: {best_fitness:.6f}")

        return True
    
    def fitness_function(self, params):
        """适应度函数"""
        try:
            from PyEMD import CEEMDAN
            
            trials = int(params[0])
            noise_std = params[1]
            max_imf = int(params[2])
            
            ceemdan = CEEMDAN(trials=trials, noise_width=noise_std)
            imfs = ceemdan.ceemdan(self.original_data, max_imf=max_imf)
            
            # 计算重构误差
            reconstructed = np.sum(imfs, axis=0)
            reconstruction_error = np.mean(np.abs(self.original_data - reconstructed))
            
            # 计算正交性
            orthogonality_score = 0
            for i in range(len(imfs)):
                for j in range(i+1, len(imfs)):
                    correlation = np.abs(np.corrcoef(imfs[i], imfs[j])[0, 1])
                    orthogonality_score += correlation
            
            orthogonality_score /= (len(imfs) * (len(imfs) - 1) / 2)
            
            # 综合适应度
            fitness = reconstruction_error + 0.1 * orthogonality_score
            
            return fitness
            
        except Exception as e:
            return 1e6  # 失败时返回很大的值

    def ceemdan_decomposition_with_progress(self):
        """带进度条的CEEMDAN分解"""
        print("\n" + "=" * 60)
        print("步骤4: CEEMDAN分解")
        print("=" * 60)

        if 'best_params' not in self.hho_results:
            print("❌ 请先运行HHO优化")
            return False

        try:
            from PyEMD import CEEMDAN
            import threading

            params = self.hho_results['best_params']
            print(f"📋 使用最优参数: {params}")
            print(f"📊 数据长度: {len(self.original_data)} 个数据点")
            print(f"⏱️  预计时间: {self._estimate_ceemdan_time(len(self.original_data), params)}")

            # 初始化CEEMDAN
            print(f"🚀 初始化CEEMDAN分解器...")
            ceemdan = CEEMDAN(
                trials=params['trials'],
                noise_width=params['noise_std']
            )

            # 开始分解并显示进度
            print(f"🔄 正在执行CEEMDAN分解 (trials={params['trials']}, max_imf={params['max_imf']})...")
            print(f"💡 这可能需要几分钟时间，请耐心等待...")
            print(f"📊 进度监控: 每30秒显示一次状态更新")

            # 重置进度控制标志
            self._stop_progress = False

            # 创建进度监控
            start_time = time.time()
            progress_thread = threading.Thread(target=self._show_ceemdan_progress_simple, args=(start_time,))
            progress_thread.daemon = True
            progress_thread.start()

            # 执行CEEMDAN分解
            print(f"⚡ 开始CEEMDAN分解...")
            imfs = ceemdan.ceemdan(self.original_data, max_imf=params['max_imf'])

            # 停止进度显示
            self._stop_progress = True
            time.sleep(0.3)  # 等待进度线程结束
            decomp_time = time.time() - start_time

            # 验证重构
            print(f"\n🔍 验证分解结果...")
            reconstructed = np.sum(imfs, axis=0)
            reconstruction_error = np.mean(np.abs(self.original_data - reconstructed))

            # 保存结果
            self.ceemdan_results = {
                'imfs': imfs,
                'reconstructed': reconstructed,
                'reconstruction_error': reconstruction_error,
                'decomposition_time': decomp_time,
                'parameters': params
            }

            print(f"✅ CEEMDAN分解完成: {decomp_time:.1f}秒")
            print(f"📊 获得 {len(imfs)} 个IMF分量")
            print(f"📊 重构误差: {reconstruction_error:.6f}")
            print(f"⚡ 平均每个数据点用时: {decomp_time/len(self.original_data)*1000:.2f}毫秒")

            return True

        except Exception as e:
            self._stop_progress = True
            print(f"\n❌ CEEMDAN分解失败: {e}")
            return False

    def hilbert_analysis_with_progress(self):
        """带进度条的Hilbert变换分析"""
        print("\n" + "=" * 60)
        print("步骤5: Hilbert变换分析")
        print("=" * 60)

        if 'imfs' not in self.ceemdan_results:
            print("❌ 请先运行CEEMDAN分解")
            return False

        try:
            from scipy.signal import hilbert

            imfs = self.ceemdan_results['imfs']
            print(f"🌊 对 {len(imfs)} 个IMF分量进行Hilbert分析...")

            # 创建Hilbert分析进度条
            hilbert_progress = ProgressTracker(len(imfs), "Hilbert分析", "IMF")

            hilbert_results = []

            for i, imf in enumerate(imfs):
                # Hilbert变换
                analytic_signal = hilbert(imf)
                instantaneous_amplitude = np.abs(analytic_signal)
                instantaneous_phase = np.unwrap(np.angle(analytic_signal))
                instantaneous_frequency = np.diff(instantaneous_phase) / (2.0 * np.pi)

                # 计算特征
                mean_freq = np.mean(instantaneous_frequency[instantaneous_frequency > 0])
                mean_amp = np.mean(instantaneous_amplitude)
                energy = np.sum(imf**2)

                hilbert_results.append({
                    'imf_index': i,
                    'mean_frequency': mean_freq if not np.isnan(mean_freq) else 0,
                    'mean_amplitude': mean_amp,
                    'energy': energy,
                    'energy_ratio': energy / np.sum([np.sum(imf**2) for imf in imfs]),
                    'instantaneous_amplitude': instantaneous_amplitude,
                    'instantaneous_frequency': instantaneous_frequency
                })

                hilbert_progress.update(1)

            hilbert_progress.close()

            # 智能高低频分离
            print("\n🎯 智能高低频分离...")
            separation_progress = ProgressTracker(4, "频率分离", "步骤")

            # 步骤1: 频率排序
            separation_progress.update(1)
            frequencies = [r['mean_frequency'] for r in hilbert_results]
            energies = [r['energy_ratio'] for r in hilbert_results]
            sorted_freq_indices = np.argsort(frequencies)[::-1]

            # 步骤2: 寻找分离点
            separation_progress.update(1)
            cumulative_energy = 0
            separation_point = len(imfs) // 2

            for i, idx in enumerate(sorted_freq_indices):
                cumulative_energy += energies[idx]
                if cumulative_energy >= 0.3:  # 高频部分包含30%的能量
                    separation_point = i + 1
                    break

            separation_point = max(2, min(separation_point, len(imfs) - 2))

            # 步骤3: 重构信号
            separation_progress.update(1)
            high_freq_indices = sorted_freq_indices[:separation_point]
            low_freq_indices = sorted_freq_indices[separation_point:]

            high_freq_signal = np.sum([imfs[i] for i in high_freq_indices], axis=0)
            low_freq_signal = np.sum([imfs[i] for i in low_freq_indices], axis=0)

            # 步骤4: 计算分离质量
            separation_progress.update(1)
            high_freq_energy = np.sum(high_freq_signal**2)
            low_freq_energy = np.sum(low_freq_signal**2)
            total_energy = high_freq_energy + low_freq_energy

            separation_progress.close()

            # 保存Hilbert结果
            self.hilbert_results = {
                'imf_features': hilbert_results,
                'high_freq_indices': high_freq_indices,
                'low_freq_indices': low_freq_indices,
                'high_freq_signal': high_freq_signal,
                'low_freq_signal': low_freq_signal,
                'separation_quality': {
                    'high_freq_energy_ratio': high_freq_energy / total_energy,
                    'low_freq_energy_ratio': low_freq_energy / total_energy
                }
            }

            print(f"✅ Hilbert分析完成")
            print(f"🎯 高频分量: {len(high_freq_indices)} 个IMF (IMF {[i+1 for i in high_freq_indices]})")
            print(f"🎯 低频分量: {len(low_freq_indices)} 个IMF (IMF {[i+1 for i in low_freq_indices]})")
            print(f"📊 高频能量占比: {high_freq_energy/total_energy:.1%}")
            print(f"📊 低频能量占比: {low_freq_energy/total_energy:.1%}")

            return True

        except Exception as e:
            print(f"❌ Hilbert分析失败: {e}")
            return False

    def save_results_with_progress(self):
        """带进度条的结果保存"""
        print("\n" + "=" * 60)
        print("步骤6: 保存结果")
        print("=" * 60)

        save_progress = ProgressTracker(5, "保存结果", "文件")

        try:
            # 步骤1: 保存主要分解结果
            save_progress.update(1)
            results_df = pd.DataFrame({
                'Original': self.original_data,
                'High_Freq': self.hilbert_results['high_freq_signal'],
                'Low_Freq': self.hilbert_results['low_freq_signal'],
                'Reconstructed': self.ceemdan_results['reconstructed']
            })

            # 添加所有IMF
            for i, imf in enumerate(self.ceemdan_results['imfs']):
                results_df[f'IMF_{i+1}'] = imf

            main_file = os.path.join(self.results_dir, 'decomposition_results_with_progress.csv')
            results_df.to_csv(main_file, index=False)

            # 步骤2: 保存Hilbert分析结果
            save_progress.update(1)
            hilbert_df = pd.DataFrame(self.hilbert_results['imf_features'])
            hilbert_file = os.path.join(self.results_dir, 'hilbert_analysis_with_progress.csv')
            hilbert_df.to_csv(hilbert_file, index=False)

            # 步骤3: 保存HHO优化结果
            save_progress.update(1)
            import json
            hho_file = os.path.join(self.results_dir, 'hho_optimization_with_progress.json')
            with open(hho_file, 'w') as f:
                json.dump(self.hho_results, f, indent=2)

            # 步骤4: 保存完整摘要
            save_progress.update(1)
            summary = {
                'data_info': {
                    'data_length': len(self.original_data),
                    'data_range': [float(np.min(self.original_data)), float(np.max(self.original_data))]
                },
                'hho_results': self.hho_results,
                'ceemdan_results': {
                    'imf_count': len(self.ceemdan_results['imfs']),
                    'reconstruction_error': float(self.ceemdan_results['reconstruction_error']),
                    'decomposition_time': float(self.ceemdan_results['decomposition_time'])
                },
                'hilbert_results': {
                    'high_freq_imfs': [int(i+1) for i in self.hilbert_results['high_freq_indices']],
                    'low_freq_imfs': [int(i+1) for i in self.hilbert_results['low_freq_indices']],
                    'separation_quality': self.hilbert_results['separation_quality']
                }
            }

            summary_file = os.path.join(self.results_dir, 'complete_summary_with_progress.json')
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)

            # 步骤5: 生成报告
            save_progress.update(1)
            report_file = os.path.join(self.results_dir, 'decomposition_report_with_progress.txt')
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("HHO-CEEMDAN-Hilbert功率分解报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"数据信息:\n")
                f.write(f"  - 数据长度: {len(self.original_data)}\n")
                f.write(f"  - 数据范围: [{np.min(self.original_data):.4f}, {np.max(self.original_data):.4f}]\n\n")
                f.write(f"HHO优化结果:\n")
                f.write(f"  - 最优参数: {self.hho_results['best_params']}\n")
                if self.hho_results['best_fitness'] is not None:
                    f.write(f"  - 最优适应度: {self.hho_results['best_fitness']:.6f}\n\n")
                else:
                    f.write(f"  - 最优适应度: 未进行优化 (使用默认参数)\n\n")
                f.write(f"CEEMDAN分解结果:\n")
                f.write(f"  - IMF数量: {len(self.ceemdan_results['imfs'])}\n")
                f.write(f"  - 重构误差: {self.ceemdan_results['reconstruction_error']:.6f}\n")
                f.write(f"  - 分解时间: {self.ceemdan_results['decomposition_time']:.1f}秒\n\n")
                f.write(f"高低频分离结果:\n")
                f.write(f"  - 高频分量: IMF {[i+1 for i in self.hilbert_results['high_freq_indices']]}\n")
                f.write(f"  - 低频分量: IMF {[i+1 for i in self.hilbert_results['low_freq_indices']]}\n")
                f.write(f"  - 高频能量占比: {self.hilbert_results['separation_quality']['high_freq_energy_ratio']:.1%}\n")
                f.write(f"  - 低频能量占比: {self.hilbert_results['separation_quality']['low_freq_energy_ratio']:.1%}\n")

            save_progress.close()

            print(f"✅ 结果保存完成")
            print(f"📁 主要结果: {main_file}")
            print(f"📁 Hilbert分析: {hilbert_file}")
            print(f"📁 HHO优化: {hho_file}")
            print(f"📁 完整摘要: {summary_file}")
            print(f"📁 分解报告: {report_file}")

            return True

        except Exception as e:
            save_progress.close()
            print(f"❌ 结果保存失败: {e}")
            return False

    def generate_frequency_datasets(self):
        """生成高频和低频功率数据集"""
        print("\n" + "=" * 60)
        print("步骤7: 生成高频和低频功率数据集")
        print("=" * 60)

        try:
            # 检查必要数据是否存在
            if not hasattr(self, 'original_data') or not hasattr(self, 'hilbert_results'):
                print("❌ 缺少必要的分解数据")
                return False

            # 加载原始完整数据集
            original_df = pd.read_csv(self.data_file)
            original_length = len(original_df)
            decomp_length = len(self.original_data)

            print(f"📊 数据长度对比:")
            print(f"   原始数据: {original_length:,} 点")
            print(f"   分解数据: {decomp_length:,} 点")
            print(f"   采样比例: 1:{original_length//decomp_length}")

            # 插值分解结果到原始长度
            print("🔄 插值分解结果到原始长度...")

            original_indices = np.arange(original_length)
            decomp_indices = np.arange(0, original_length, original_length // decomp_length)[:decomp_length]

            # 对高频和低频分量进行插值
            high_freq_interp = np.interp(original_indices, decomp_indices,
                                       self.hilbert_results['high_freq_signal'])
            low_freq_interp = np.interp(original_indices, decomp_indices,
                                      self.hilbert_results['low_freq_signal'])

            print(f"✅ 插值完成: {len(high_freq_interp):,} 点")

            # 创建高频功率数据集
            print("📈 创建高频功率数据集...")
            high_freq_df = original_df.copy()
            high_freq_df[self.power_column] = high_freq_interp

            # 根据输入文件名生成输出文件名
            input_filename = os.path.basename(self.data_file)
            base_name = os.path.splitext(input_filename)[0]
            high_freq_file = os.path.join(self.results_dir, f'{base_name}_high_frequency_power.csv')
            high_freq_df.to_csv(high_freq_file, index=False)
            print(f"✅ 高频数据集保存到: {high_freq_file}")

            # 保存文件名供后续使用
            self.generated_files['high_freq'] = f'{base_name}_high_frequency_power.csv'

            # 创建低频功率数据集
            print("📈 创建低频功率数据集...")
            low_freq_df = original_df.copy()
            low_freq_df[self.power_column] = low_freq_interp

            low_freq_file = os.path.join(self.results_dir, f'{base_name}_low_frequency_power.csv')
            low_freq_df.to_csv(low_freq_file, index=False)
            print(f"✅ 低频数据集保存到: {low_freq_file}")

            # 保存文件名供后续使用
            self.generated_files['low_freq'] = f'{base_name}_low_frequency_power.csv'

            # 数据统计信息
            original_power = original_df[self.power_column]

            print(f"\n📊 数据统计信息:")
            print(f"原始功率: 均值={np.mean(original_power):.3f}, 标准差={np.std(original_power):.3f}")
            print(f"高频功率: 均值={np.mean(high_freq_interp):.3f}, 标准差={np.std(high_freq_interp):.3f}")
            print(f"低频功率: 均值={np.mean(low_freq_interp):.3f}, 标准差={np.std(low_freq_interp):.3f}")

            # 验证重构精度
            reconstructed = high_freq_interp + low_freq_interp
            reconstruction_error = np.mean(np.abs(original_power - reconstructed))
            correlation = np.corrcoef(original_power, reconstructed)[0, 1]

            print(f"\n🔍 重构验证:")
            print(f"   重构误差: {reconstruction_error:.6f}")
            print(f"   相关系数: {correlation:.6f}")

            # 创建数据集说明文件
            self._create_dataset_readme(original_df, high_freq_interp, low_freq_interp,
                                      reconstruction_error, correlation, base_name)

            print("✅ 高频和低频功率数据集生成完成!")
            return True

        except Exception as e:
            print(f"❌ 数据集生成失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _create_dataset_readme(self, original_df, high_freq_data, low_freq_data,
                              reconstruction_error, correlation, base_name):
        """创建数据集说明文件"""
        original_power = original_df[self.power_column]

        readme_content = f"""# 高频和低频功率数据集说明

## 数据集概述

本目录包含基于HHO-CEEMDAN-Hilbert算法分解的风电功率数据集：

### 文件列表
- `{base_name}_high_frequency_power.csv` - 高频功率数据集
- `{base_name}_low_frequency_power.csv` - 低频功率数据集

### 数据格式
数据格式与原始数据集完全相同：
- 包含相同的时间戳和气象特征列
- 最后一列 `{self.power_column}` 被替换为分解后的高频/低频功率

### 数据统计
- **数据长度**: {len(original_df):,} 行
- **时间范围**: {original_df.iloc[0, 0]} 至 {original_df.iloc[-1, 0]}
- **采样间隔**: 15分钟

#### 原始功率统计
- 均值: {np.mean(original_power):.6f}
- 标准差: {np.std(original_power):.6f}
- 范围: [{np.min(original_power):.6f}, {np.max(original_power):.6f}]

#### 高频功率统计
- 均值: {np.mean(high_freq_data):.6f}
- 标准差: {np.std(high_freq_data):.6f}
- 范围: [{np.min(high_freq_data):.6f}, {np.max(high_freq_data):.6f}]

#### 低频功率统计
- 均值: {np.mean(low_freq_data):.6f}
- 标准差: {np.std(low_freq_data):.6f}
- 范围: [{np.min(low_freq_data):.6f}, {np.max(low_freq_data):.6f}]

### 分解质量
- **重构误差**: {reconstruction_error:.6f}
- **相关系数**: {correlation:.6f}

### 分解参数
基于HHO优化的最优CEEMDAN参数：
- trials: {self.hho_results['best_params']['trials']}
- noise_std: {self.hho_results['best_params']['noise_std']:.6f}
- max_imf: {self.hho_results['best_params']['max_imf']}

### 高低频分离
- **高频分量**: IMF {[i+1 for i in self.hilbert_results['high_freq_indices']]}，占{self.hilbert_results['separation_quality']['high_freq_energy_ratio']:.1%}能量
- **低频分量**: IMF {[i+1 for i in self.hilbert_results['low_freq_indices']]}，占{self.hilbert_results['separation_quality']['low_freq_energy_ratio']:.1%}能量

### 使用建议
1. **高频数据集**适用于：
   - 短期功率预测（小时级）
   - 功率波动分析
   - 异常检测

2. **低频数据集**适用于：
   - 长期趋势预测（日级、周级）
   - 季节性分析
   - 容量规划

### 生成时间
{pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}

### 算法参考
- HHO: Harris Hawks Optimizer
- CEEMDAN: Complete Ensemble Empirical Mode Decomposition with Adaptive Noise
- Hilbert Transform: 瞬时频率和振幅分析
"""

        readme_file = os.path.join(self.results_dir, '数据集说明.md')
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"✅ 数据集说明保存到: {readme_file}")

    def run_complete_pipeline(self, sample_rate=None, max_iter=None, n_hawks=None):
        """运行完整的分解流水线"""
        print("🚀 带详细进度条的HHO-CEEMDAN-Hilbert功率分解")
        print("🎯 每个计算步骤都有实时进度显示")
        print("⚡ 使用完整数据集获得最高精度，PE增强分离")
        print("=" * 80)

        # 使用配置中的参数
        if sample_rate is None:
            sample_rate = self.sample_rate
        if max_iter is None:
            max_iter = self.config['hho_config']['max_iter']
        if n_hawks is None:
            n_hawks = self.config['hho_config']['n_hawks']

        print(f"📊 配置参数:")
        print(f"   - 使用完整数据: {'是' if self.use_full_data else '否'}")
        print(f"   - HHO优化启用: {'是' if self.config['hho_config']['enable_optimization'] else '否'}")
        print(f"   - HHO最大迭代: {max_iter}")
        print(f"   - HHO鹰群数量: {n_hawks}")
        print(f"   - 采样率: {sample_rate}")
        if not self.config['hho_config']['enable_optimization']:
            print(f"   - 默认CEEMDAN参数: {self.config['default_ceemdan_params']}")

        start_time = time.time()

        # 步骤1: 数据加载
        if not self.load_data_with_progress(sample_rate):
            return False

        # 步骤2: 频率分析
        if not self.frequency_analysis_with_progress():
            return False

        # 步骤3: HHO优化（根据配置决定是否执行）
        if self.config['hho_config']['enable_optimization']:
            print(f"🦅 HHO优化已启用")
            # 使用配置中的HHO参数
            hho_max_iter = self.config['hho_config']['max_iter']
            hho_n_hawks = self.config['hho_config']['n_hawks']
            if not self.hho_optimization_with_progress(hho_max_iter, hho_n_hawks):
                return False
        else:
            print(f"⏭️  跳过HHO优化（配置中已禁用）")
            print(f"📋 将使用默认CEEMDAN参数: {self.config['default_ceemdan_params']}")
            # 使用默认参数
            self.hho_results = {
                'best_params': self.config['default_ceemdan_params'],
                'best_fitness': None,
                'convergence_curve': []
            }

        # 步骤4: CEEMDAN分解
        if not self.ceemdan_decomposition_with_progress():
            return False

        # 步骤5: Hilbert分析
        if not self.hilbert_analysis_with_progress():
            return False

        # 步骤6: 保存结果
        if not self.save_results_with_progress():
            return False

        # 步骤7: 生成高频和低频功率数据集
        if not self.generate_frequency_datasets():
            return False

        # 总结
        total_time = time.time() - start_time
        print("\n" + "🎉" + "=" * 78)
        print("✅ 完整分解流水线执行成功!")
        print(f"⏱️  总执行时间: {total_time/60:.1f} 分钟")
        print(f"📊 数据点数: {len(self.original_data)}")
        print(f"🏆 最优参数: {self.hho_results['best_params']}")
        print(f"📈 IMF分量: {len(self.ceemdan_results['imfs'])} 个")
        print(f"🎯 高频分量: {len(self.hilbert_results['high_freq_indices'])} 个IMF")
        print(f"🎯 低频分量: {len(self.hilbert_results['low_freq_indices'])} 个IMF")
        print(f"📁 结果保存在: {self.results_dir}")
        print(f"📊 高频数据集: {self.generated_files.get('high_freq', 'high_frequency_power.csv')}")
        print(f"📊 低频数据集: {self.generated_files.get('low_freq', 'low_frequency_power.csv')}")
        print("🎉" + "=" * 78)

        return True

    def _estimate_ceemdan_time(self, data_length, params):
        """估算CEEMDAN分解时间"""
        # 基于经验公式估算时间
        base_time = 0.0008  # 每个数据点的基础时间(秒)
        trials_factor = params['trials'] / 30  # trials影响因子
        imf_factor = params['max_imf'] / 7     # IMF数量影响因子

        estimated_seconds = data_length * base_time * trials_factor * imf_factor

        if estimated_seconds < 60:
            return f"约 {estimated_seconds:.0f} 秒"
        elif estimated_seconds < 3600:
            return f"约 {estimated_seconds/60:.1f} 分钟"
        else:
            return f"约 {estimated_seconds/3600:.1f} 小时"

    def _show_ceemdan_progress(self, start_time):
        """显示CEEMDAN分解的动态进度"""
        import time

        symbols = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
        i = 0

        try:
            while not self._stop_progress:
                elapsed = time.time() - start_time

                # 格式化时间
                if elapsed < 60:
                    time_str = f"{elapsed:.0f}s"
                else:
                    minutes = int(elapsed // 60)
                    seconds = int(elapsed % 60)
                    time_str = f"{minutes}m{seconds:02d}s"

                # 显示动态进度
                progress_msg = f"{symbols[i % len(symbols)]} CEEMDAN分解进行中... 已用时: {time_str}"
                print(f"\r{progress_msg:<60}", end='', flush=True)

                time.sleep(0.3)
                i += 1

        except Exception:
            pass
        finally:
            # 清除进度行
            print(f"\r{' ' * 70}\r", end='', flush=True)

    def _show_ceemdan_progress_simple(self, start_time):
        """显示CEEMDAN分解的简单进度（更兼容的版本）"""
        import time

        last_update = 0
        update_interval = 30  # 每30秒更新一次

        try:
            while not self._stop_progress:
                elapsed = time.time() - start_time

                # 每30秒或开始时显示一次状态
                if elapsed - last_update >= update_interval or last_update == 0:
                    if elapsed < 60:
                        time_str = f"{elapsed:.0f}秒"
                    else:
                        minutes = int(elapsed // 60)
                        seconds = int(elapsed % 60)
                        time_str = f"{minutes}分{seconds:02d}秒"

                    print(f"📊 CEEMDAN分解进行中... 已用时: {time_str}")
                    last_update = elapsed

                time.sleep(5)  # 每5秒检查一次

        except Exception:
            pass

def main():
    """主函数"""
    print("🎯 带详细进度条的HHO-CEEMDAN-Hilbert功率分解工具")
    print("📊 为每个计算步骤提供实时进度显示")
    print("=" * 80)

    try:
        # 创建分解器实例
        pipeline = PowerDecompositionWithProgress()

        # 运行完整流水线
        success = pipeline.run_complete_pipeline(
            sample_rate=20,    # 数据采样率
            max_iter=15,       # HHO迭代次数
            n_hawks=8          # HHO鹰群数量
        )

        if success:
            print("\n💡 下一步建议:")
            print("1. 检查results/文件夹中的分解结果")
            print("2. 查看decomposition_report_with_progress.txt了解详细信息")
            print("3. 使用分解后的高低频分量进行预测建模")
        else:
            print("\n❌ 分解流水线执行失败")

        return success

    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断程序")
        return False
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
