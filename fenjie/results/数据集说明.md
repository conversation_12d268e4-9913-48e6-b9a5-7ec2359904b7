# 高频和低频功率数据集说明

## 数据集概述

本目录包含基于HHO-CEEMDAN-Hilbert算法分解的风电功率数据集：

### 文件列表
- `Wind farm site 6 (Nominal capacity-96MW)_high_frequency_power.csv` - 高频功率数据集
- `Wind farm site 6 (Nominal capacity-96MW)_low_frequency_power.csv` - 低频功率数据集

### 数据格式
数据格式与原始数据集完全相同：
- 包含相同的时间戳和气象特征列
- 最后一列 `Power (MW)` 被替换为分解后的高频/低频功率

### 数据统计
- **数据长度**: 70,176 行
- **时间范围**: 2019-01-01 00:00:00 至 2020-12-31 23:45:00
- **采样间隔**: 15分钟

#### 原始功率统计
- 均值: 28.761022
- 标准差: 28.012673
- 范围: [-1.316000, 114.360000]

#### 高频功率统计
- 均值: -0.021482
- 标准差: 4.719435
- 范围: [-45.645231, 105.745087]

#### 低频功率统计
- 均值: 28.782504
- 标准差: 27.642431
- 范围: [-17.926215, 100.848971]

### 分解质量
- **重构误差**: 0.000000
- **相关系数**: 1.000000

### 分解参数
基于HHO优化的最优CEEMDAN参数：
- trials: 30
- noise_std: 0.010000
- max_imf: 7

### 高低频分离
- **高频分量**: IMF [1, 2, 3, 4, 5, 6]，占1.4%能量
- **低频分量**: IMF [7, 8]，占98.6%能量

### 使用建议
1. **高频数据集**适用于：
   - 短期功率预测（小时级）
   - 功率波动分析
   - 异常检测

2. **低频数据集**适用于：
   - 长期趋势预测（日级、周级）
   - 季节性分析
   - 容量规划

### 生成时间
2025-07-30 05:41:14

### 算法参考
- HHO: Harris Hawks Optimizer
- CEEMDAN: Complete Ensemble Empirical Mode Decomposition with Adaptive Noise
- Hilbert Transform: 瞬时频率和振幅分析
